package handler

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"xr-gallery/internal/repository"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// DeleteXRID 删除指定xrid的所有数据处理器
func DeleteXRID(ctx context.Context, c *app.RequestContext) {
	// 获取路径参数中的xrid
	xridStr := c.Param("xrid")
	if xridStr == "" {
		log.Printf("❌ 缺少xrid参数")
		c.<PERSON><PERSON>("Content-Type", "application/json; charset=utf-8")
		c.J<PERSON>(400, utils.H{
			"success": false,
			"error":   "缺少xrid参数",
		})
		return
	}

	// 转换xrid为整数
	xrid, err := strconv.Atoi(xridStr)
	if err != nil || xrid <= 0 {
		log.Printf("❌ 无效的xrid参数: %s", xridStr)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.J<PERSON>(400, utils.H{
			"success": false,
			"error":   fmt.Sprintf("无效的xrid参数: %s", xridStr),
		})
		return
	}

	log.Printf("🗑️ 开始删除xrid=%d的所有数据", xrid)

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 执行删除操作
	deletedCount, err := repo.DeleteXRIDData(xrid)
	if err != nil {
		log.Printf("❌ 删除数据失败: xrid=%d, error=%v", xrid, err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("删除数据失败: %v", err),
			"xrid":    xrid,
		})
		return
	}

	log.Printf("✅ 删除完成: xrid=%d, 删除了%d条xrinfo记录, 重置了xr表状态", xrid, deletedCount)

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"success":       true,
		"xrid":          xrid,
		"deleted_count": deletedCount,
		"message":       fmt.Sprintf("成功删除xrid=%d的%d条记录并重置状态", xrid, deletedCount),
		"operations": []string{
			fmt.Sprintf("删除xrinfo表中%d条记录", deletedCount),
			"重置xr表中issave状态为0",
		},
	})
}
