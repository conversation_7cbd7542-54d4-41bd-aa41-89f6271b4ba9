# =============================================================================
# XR Gallery Project - Multi-Language .gitignore
# Frontend (Vue.js/Node.js) + Backend (Go) + xr-rust (Rust)
# =============================================================================

# =============================================================================
# Frontend (Vue.js/Node.js/JavaScript)
# =============================================================================

# Dependencies
frontend/node_modules/
frontend/pnpm-lock.yaml
frontend/yarn.lock
frontend/package-lock.json

# Build outputs
frontend/dist/
frontend/dist-ssr/
frontend/build/

# Vite
frontend/.vite/
frontend/vite.config.js.timestamp-*

# Testing
frontend/test-results/
frontend/playwright-report/
frontend/coverage/
frontend/tests/screenshots/

# =============================================================================
# Backend (Go)
# =============================================================================

# Go build outputs
backend/dist/
backend/tmp/
backend/*.exe
backend/main
backend/main.exe
backend/xr-gallery
backend/xr-gallery.exe

# Go modules cache
backend/go.sum.backup

# Air hot reload
backend/build-errors.log
backend/.air.toml.backup

# Go test outputs
backend/*.test
backend/*.prof

# Development scripts output
backend/nodejs-test/

# =============================================================================
# xr-rust (Rust)
# =============================================================================

# Rust build outputs
xr-rust/target/
xr-rust/Cargo.lock.backup

# Rust executables
xr-rust/xr-crawler-*
xr-rust/*.exe

# Cross compilation
xr-rust/.cross/

# =============================================================================
# Environment & Configuration
# =============================================================================

# Environment variables
.env
.env.local
.env.*.local
.env.production
.env.development

# Configuration backups
config/*.backup
config/*.bak
*.yaml.backup
*.yml.backup

# Dynamic URLs (these change frequently)
finalUrl.txt.backup
*/finalUrl.txt.backup

# =============================================================================
# Development Tools & IDE
# =============================================================================

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# =============================================================================
# Operating System
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory
.Trash-*

# =============================================================================
# Logs & Runtime
# =============================================================================

# Logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# =============================================================================
# Temporary & Cache
# =============================================================================

# Temporary folders
tmp/
temp/
.tmp/
.cache/

# OS cache
.DS_Store?
.Spotlight-V100
.Trashes

# =============================================================================
# Deployment & Production
# =============================================================================

# Docker
.dockerignore.backup
docker-compose.override.yml

# Deployment artifacts
deploy/*.backup
deploy/logs/
logs/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# =============================================================================
# Database & Data
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3

# Data dumps
*.sql.backup
*.dump

# =============================================================================
# Miscellaneous
# =============================================================================

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Backup files
*.backup
*.bak
*.orig
*.save

# Editor temp files
.#*
#*#