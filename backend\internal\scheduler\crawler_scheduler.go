package scheduler

import (
	"context"
	"log"
	"sync"
	"time"
	"xr-gallery/internal/service"
)

// CrawlerScheduler 爬虫定时调度器
type CrawlerScheduler struct {
	crawlerService *service.CrawlerService
	urlManager     *service.URLManager
	ticker         *time.Ticker
	urlCheckTicker *time.Ticker
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	isRunning      bool
	mu             sync.RWMutex

	// 配置参数
	interval         time.Duration
	maxPages         int
	currentPage      int
	urlCheckInterval time.Duration

	// 统计信息
	totalRuns     int
	totalInserted int
	totalUpdated  int
	lastRunTime   time.Time
	lastError     error
	lastURLCheck  time.Time
}

// NewCrawlerScheduler 创建爬虫调度器实例
func NewCrawlerScheduler() *CrawlerScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	return &CrawlerScheduler{
		crawlerService:   service.NewCrawlerService(),
		urlManager:       service.NewURLManager(),
		ctx:              ctx,
		cancel:           cancel,
		interval:         10 * time.Minute, // 每10分钟执行一次
		maxPages:         2,                // 最多爬取2页，避免过度请求
		currentPage:      1,
		urlCheckInterval: 60 * time.Minute, // 每5分钟检查一次URL
	}
}

// Start 启动定时器
func (s *CrawlerScheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return nil
	}

	log.Printf("🚀 启动爬虫定时器: 间隔%v, 最大页数%d", s.interval, s.maxPages)
	log.Printf("🔄 启动URL检查定时器: 间隔%v", s.urlCheckInterval)

	s.ticker = time.NewTicker(s.interval)
	s.urlCheckTicker = time.NewTicker(s.urlCheckInterval)
	s.isRunning = true

	s.wg.Add(2)
	go s.run()
	go s.runURLCheck()

	return nil
}

// Stop 停止定时器
func (s *CrawlerScheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	log.Printf("🛑 停止爬虫定时器")

	s.isRunning = false
	s.cancel()

	if s.ticker != nil {
		s.ticker.Stop()
	}

	if s.urlCheckTicker != nil {
		s.urlCheckTicker.Stop()
	}

	s.wg.Wait()
	log.Printf("✅ 爬虫定时器已停止")
}

// run 定时器主循环
func (s *CrawlerScheduler) run() {
	defer s.wg.Done()

	// 立即执行一次
	s.executeTask()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.ticker.C:
			s.executeTask()
		}
	}
}

// executeTask 执行爬虫任务
func (s *CrawlerScheduler) executeTask() {
	s.mu.Lock()
	currentPage := s.currentPage
	s.mu.Unlock()

	startTime := time.Now()
	log.Printf("⏰ 定时任务开始: 第%d页 [%s]", currentPage, startTime.Format("2006-01-02 15:04:05"))

	// 爬取列表页数据
	items, err := s.crawlerService.GetListPage(currentPage)
	if err != nil {
		s.mu.Lock()
		s.lastError = err
		s.mu.Unlock()
		log.Printf("❌ 定时任务失败: %v", err)
		return
	}

	if len(items) == 0 {
		log.Printf("⚠️  第%d页无数据，重置到第1页", currentPage)
		s.mu.Lock()
		s.currentPage = 1
		s.mu.Unlock()
		return
	}

	// 保存或更新数据
	insertCount, updateCount, err := s.crawlerService.SaveOrUpdateItems(items)
	if err != nil {
		s.mu.Lock()
		s.lastError = err
		s.mu.Unlock()
		log.Printf("❌ 数据保存失败: %v", err)
		return
	}

	// 更新统计信息
	s.mu.Lock()
	s.totalRuns++
	s.totalInserted += insertCount
	s.totalUpdated += updateCount
	s.lastRunTime = startTime
	s.lastError = nil

	// 更新页码（循环爬取）
	s.currentPage++
	if s.currentPage > s.maxPages {
		s.currentPage = 1
	}
	s.mu.Unlock()

	duration := time.Since(startTime)
	log.Printf("✅ 定时任务完成: 第%d页, 耗时%v, 爬取%d条, 插入%d条, 更新%d条",
		currentPage, duration, len(items), insertCount, updateCount)
}

// runURLCheck URL检查定时器主循环
func (s *CrawlerScheduler) runURLCheck() {
	defer s.wg.Done()

	// 立即执行一次URL检查
	s.executeURLCheck()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.urlCheckTicker.C:
			s.executeURLCheck()
		}
	}
}

// executeURLCheck 执行URL检查任务
func (s *CrawlerScheduler) executeURLCheck() {
	startTime := time.Now()
	log.Printf("🔄 URL检查任务开始 [%s]", startTime.Format("2006-01-02 15:04:05"))

	err := s.urlManager.CheckAndUpdateBaseURL()

	s.mu.Lock()
	s.lastURLCheck = startTime
	s.mu.Unlock()

	duration := time.Since(startTime)
	if err != nil {
		log.Printf("❌ URL检查任务失败: %v, 耗时%v", err, duration)
	} else {
		log.Printf("✅ URL检查任务完成, 耗时%v", duration)
	}
}

// GetStats 获取统计信息
func (s *CrawlerScheduler) GetStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var lastErrorStr string
	if s.lastError != nil {
		lastErrorStr = s.lastError.Error()
	}

	// 获取当前URL信息
	currentURL, _ := s.urlManager.GetCurrentURL()
	baseURL, _ := s.urlManager.GetBaseURL()

	return map[string]interface{}{
		"is_running":         s.isRunning,
		"interval":           s.interval.String(),
		"max_pages":          s.maxPages,
		"current_page":       s.currentPage,
		"total_runs":         s.totalRuns,
		"total_inserted":     s.totalInserted,
		"total_updated":      s.totalUpdated,
		"last_run_time":      s.lastRunTime.Format("2006-01-02 15:04:05"),
		"last_error":         lastErrorStr,
		"next_run_time":      time.Now().Add(s.interval).Format("2006-01-02 15:04:05"),
		"url_check_interval": s.urlCheckInterval.String(),
		"last_url_check":     s.lastURLCheck.Format("2006-01-02 15:04:05"),
		"next_url_check":     time.Now().Add(s.urlCheckInterval).Format("2006-01-02 15:04:05"),
		"base_url":           baseURL,
		"current_url":        currentURL,
	}
}

// SetInterval 设置执行间隔
func (s *CrawlerScheduler) SetInterval(interval time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.interval = interval

	if s.isRunning && s.ticker != nil {
		s.ticker.Stop()
		s.ticker = time.NewTicker(interval)
	}

	log.Printf("🔧 更新定时器间隔: %v", interval)
}

// SetMaxPages 设置最大爬取页数
func (s *CrawlerScheduler) SetMaxPages(maxPages int) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.maxPages = maxPages
	log.Printf("🔧 更新最大页数: %d", maxPages)
}

// IsRunning 检查是否正在运行
func (s *CrawlerScheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}
