package scheduler

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"time"
	"xr-gallery/internal/service"
)

// CrawlerScheduler 爬虫定时调度器
type CrawlerScheduler struct {
	crawlerService *service.CrawlerService
	urlManager     *service.URLManager
	ticker         *time.Ticker
	urlCheckTicker *time.Ticker
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	isRunning      bool
	mu             sync.RWMutex

	// HTTP端点配置
	baseURL string

	// 新增的5个定时器
	reurlTicker   *time.Ticker // reurl定时器 - 每10秒
	refmTicker    *time.Ticker // refm定时器 - 每30秒
	imglistTicker *time.Ticker // imglist定时器 - 每60秒
	cleanupTicker *time.Ticker // cleanup定时器 - 每3小时
	retryTicker   *time.Ticker // retry定时器 - 每15分钟

	// 新增定时器的处理状态
	reurlProcessing   bool
	refmProcessing    bool
	imglistProcessing bool
	cleanupProcessing bool
	retryProcessing   bool

	// 配置参数
	interval         time.Duration
	maxPages         int
	currentPage      int
	urlCheckInterval time.Duration

	// 新增定时器的时间间隔
	reurlInterval   time.Duration
	refmInterval    time.Duration
	imglistInterval time.Duration
	cleanupInterval time.Duration
	retryInterval   time.Duration

	// 统计信息
	totalRuns     int
	totalInserted int
	totalUpdated  int
	lastRunTime   time.Time
	lastError     error
	lastURLCheck  time.Time

	// 新增定时器的统计信息
	reurlRuns       int
	refmRuns        int
	imglistRuns     int
	cleanupRuns     int
	retryRuns       int
	lastReurlTime   time.Time
	lastRefmTime    time.Time
	lastImglistTime time.Time
	lastCleanupTime time.Time
	lastRetryTime   time.Time
}

// NewCrawlerScheduler 创建爬虫调度器实例
func NewCrawlerScheduler(baseURL string) *CrawlerScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	return &CrawlerScheduler{
		crawlerService:   service.NewCrawlerService(),
		urlManager:       service.NewURLManager(),
		ctx:              ctx,
		cancel:           cancel,
		baseURL:          baseURL,          // HTTP端点基础URL
		interval:         10 * time.Minute, // 每10分钟执行一次
		maxPages:         2,                // 最多爬取2页，避免过度请求
		currentPage:      1,
		urlCheckInterval: 5 * time.Minute, // 每5分钟检查一次URL

		// 新增定时器的时间间隔 - 与Rust版本一致
		reurlInterval:   10 * time.Second, // 每10秒处理一张内页图片上传
		refmInterval:    30 * time.Second, // 每30秒处理一张封面图片上传
		imglistInterval: 60 * time.Second, // 每60秒爬取内页图片
		cleanupInterval: 3 * time.Hour,    // 每3小时清理无效数据
		retryInterval:   15 * time.Minute, // 每15分钟重试失败的上传
	}
}

// Start 启动定时器
func (s *CrawlerScheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return nil
	}

	log.Printf("🚀 启动爬虫定时器: 间隔%v, 最大页数%d", s.interval, s.maxPages)
	log.Printf("🔄 启动URL检查定时器: 间隔%v", s.urlCheckInterval)

	// 启动原有定时器
	s.ticker = time.NewTicker(s.interval)
	s.urlCheckTicker = time.NewTicker(s.urlCheckInterval)

	// 启动新增的5个定时器
	s.reurlTicker = time.NewTicker(s.reurlInterval)
	s.refmTicker = time.NewTicker(s.refmInterval)
	s.imglistTicker = time.NewTicker(s.imglistInterval)
	s.cleanupTicker = time.NewTicker(s.cleanupInterval)
	s.retryTicker = time.NewTicker(s.retryInterval)

	log.Printf("🖼️ 启动reurl定时器: 间隔%v", s.reurlInterval)
	log.Printf("🎨 启动refm定时器: 间隔%v", s.refmInterval)
	log.Printf("📸 启动imglist定时器: 间隔%v", s.imglistInterval)
	log.Printf("🧹 启动cleanup定时器: 间隔%v", s.cleanupInterval)
	log.Printf("🔄 启动retry定时器: 间隔%v", s.retryInterval)

	s.isRunning = true

	// 启动所有goroutine
	s.wg.Add(7)
	go s.run()
	go s.runURLCheck()
	go s.runReurl()
	go s.runRefm()
	go s.runImglist()
	go s.runCleanup()
	go s.runRetry()

	return nil
}

// Stop 停止定时器
func (s *CrawlerScheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	log.Printf("🛑 停止爬虫定时器")

	s.isRunning = false
	s.cancel()

	// 停止原有定时器
	if s.ticker != nil {
		s.ticker.Stop()
	}

	if s.urlCheckTicker != nil {
		s.urlCheckTicker.Stop()
	}

	// 停止新增的5个定时器
	if s.reurlTicker != nil {
		s.reurlTicker.Stop()
	}

	if s.refmTicker != nil {
		s.refmTicker.Stop()
	}

	if s.imglistTicker != nil {
		s.imglistTicker.Stop()
	}

	if s.cleanupTicker != nil {
		s.cleanupTicker.Stop()
	}

	if s.retryTicker != nil {
		s.retryTicker.Stop()
	}

	s.wg.Wait()
	log.Printf("✅ 爬虫定时器已停止")
}

// run 定时器主循环
func (s *CrawlerScheduler) run() {
	defer s.wg.Done()

	// 立即执行一次
	s.executeTask()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.ticker.C:
			s.executeTask()
		}
	}
}

// executeTask 执行爬虫任务
func (s *CrawlerScheduler) executeTask() {
	s.mu.Lock()
	currentPage := s.currentPage
	s.mu.Unlock()

	startTime := time.Now()
	log.Printf("⏰ 定时任务开始: 第%d页 [%s]", currentPage, startTime.Format("2006-01-02 15:04:05"))

	// 爬取列表页数据
	items, err := s.crawlerService.GetListPage(currentPage)
	if err != nil {
		s.mu.Lock()
		s.lastError = err
		s.mu.Unlock()
		log.Printf("❌ 定时任务失败: %v", err)
		return
	}

	if len(items) == 0 {
		log.Printf("⚠️  第%d页无数据，重置到第1页", currentPage)
		s.mu.Lock()
		s.currentPage = 1
		s.mu.Unlock()
		return
	}

	// 保存或更新数据
	insertCount, updateCount, err := s.crawlerService.SaveOrUpdateItems(items)
	if err != nil {
		s.mu.Lock()
		s.lastError = err
		s.mu.Unlock()
		log.Printf("❌ 数据保存失败: %v", err)
		return
	}

	// 更新统计信息
	s.mu.Lock()
	s.totalRuns++
	s.totalInserted += insertCount
	s.totalUpdated += updateCount
	s.lastRunTime = startTime
	s.lastError = nil

	// 更新页码（循环爬取）
	s.currentPage++
	if s.currentPage > s.maxPages {
		s.currentPage = 1
	}
	s.mu.Unlock()

	duration := time.Since(startTime)
	log.Printf("✅ 定时任务完成: 第%d页, 耗时%v, 爬取%d条, 插入%d条, 更新%d条",
		currentPage, duration, len(items), insertCount, updateCount)
}

// runURLCheck URL检查定时器主循环
func (s *CrawlerScheduler) runURLCheck() {
	defer s.wg.Done()

	// 立即执行一次URL检查
	s.executeURLCheck()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.urlCheckTicker.C:
			s.executeURLCheck()
		}
	}
}

// executeURLCheck 执行URL检查任务
func (s *CrawlerScheduler) executeURLCheck() {
	startTime := time.Now()
	log.Printf("🔄 URL检查任务开始 [%s]", startTime.Format("2006-01-02 15:04:05"))

	err := s.urlManager.CheckAndUpdateBaseURL()

	s.mu.Lock()
	s.lastURLCheck = startTime
	s.mu.Unlock()

	duration := time.Since(startTime)
	if err != nil {
		log.Printf("❌ URL检查任务失败: %v, 耗时%v", err, duration)
	} else {
		log.Printf("✅ URL检查任务完成, 耗时%v", duration)
	}
}

// GetStats 获取统计信息
func (s *CrawlerScheduler) GetStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var lastErrorStr string
	if s.lastError != nil {
		lastErrorStr = s.lastError.Error()
	}

	// 获取当前URL信息
	currentURL, _ := s.urlManager.GetCurrentURL()
	baseURL, _ := s.urlManager.GetBaseURL()

	return map[string]interface{}{
		"is_running":         s.isRunning,
		"interval":           s.interval.String(),
		"max_pages":          s.maxPages,
		"current_page":       s.currentPage,
		"total_runs":         s.totalRuns,
		"total_inserted":     s.totalInserted,
		"total_updated":      s.totalUpdated,
		"last_run_time":      s.lastRunTime.Format("2006-01-02 15:04:05"),
		"last_error":         lastErrorStr,
		"next_run_time":      time.Now().Add(s.interval).Format("2006-01-02 15:04:05"),
		"url_check_interval": s.urlCheckInterval.String(),
		"last_url_check":     s.lastURLCheck.Format("2006-01-02 15:04:05"),
		"next_url_check":     time.Now().Add(s.urlCheckInterval).Format("2006-01-02 15:04:05"),
		"base_url":           baseURL,
		"current_url":        currentURL,

		// 新增定时器的统计信息
		"reurl_timer": map[string]interface{}{
			"interval":   s.reurlInterval.String(),
			"runs":       s.reurlRuns,
			"processing": s.reurlProcessing,
			"last_run":   s.lastReurlTime.Format("2006-01-02 15:04:05"),
			"next_run":   time.Now().Add(s.reurlInterval).Format("2006-01-02 15:04:05"),
		},
		"refm_timer": map[string]interface{}{
			"interval":   s.refmInterval.String(),
			"runs":       s.refmRuns,
			"processing": s.refmProcessing,
			"last_run":   s.lastRefmTime.Format("2006-01-02 15:04:05"),
			"next_run":   time.Now().Add(s.refmInterval).Format("2006-01-02 15:04:05"),
		},
		"imglist_timer": map[string]interface{}{
			"interval":   s.imglistInterval.String(),
			"runs":       s.imglistRuns,
			"processing": s.imglistProcessing,
			"last_run":   s.lastImglistTime.Format("2006-01-02 15:04:05"),
			"next_run":   time.Now().Add(s.imglistInterval).Format("2006-01-02 15:04:05"),
		},
		"cleanup_timer": map[string]interface{}{
			"interval":   s.cleanupInterval.String(),
			"runs":       s.cleanupRuns,
			"processing": s.cleanupProcessing,
			"last_run":   s.lastCleanupTime.Format("2006-01-02 15:04:05"),
			"next_run":   time.Now().Add(s.cleanupInterval).Format("2006-01-02 15:04:05"),
		},
		"retry_timer": map[string]interface{}{
			"interval":   s.retryInterval.String(),
			"runs":       s.retryRuns,
			"processing": s.retryProcessing,
			"last_run":   s.lastRetryTime.Format("2006-01-02 15:04:05"),
			"next_run":   time.Now().Add(s.retryInterval).Format("2006-01-02 15:04:05"),
		},
	}
}

// SetInterval 设置执行间隔
func (s *CrawlerScheduler) SetInterval(interval time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.interval = interval

	if s.isRunning && s.ticker != nil {
		s.ticker.Stop()
		s.ticker = time.NewTicker(interval)
	}

	log.Printf("🔧 更新定时器间隔: %v", interval)
}

// SetMaxPages 设置最大爬取页数
func (s *CrawlerScheduler) SetMaxPages(maxPages int) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.maxPages = maxPages
	log.Printf("🔧 更新最大页数: %d", maxPages)
}

// IsRunning 检查是否正在运行
func (s *CrawlerScheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// runReurl reurl定时器执行函数
func (s *CrawlerScheduler) runReurl() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.reurlTicker.C:
			s.executeReurl()
		}
	}
}

// runRefm refm定时器执行函数
func (s *CrawlerScheduler) runRefm() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.refmTicker.C:
			s.executeRefm()
		}
	}
}

// runImglist imglist定时器执行函数
func (s *CrawlerScheduler) runImglist() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.imglistTicker.C:
			s.executeImglist()
		}
	}
}

// runCleanup cleanup定时器执行函数
func (s *CrawlerScheduler) runCleanup() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.cleanupTicker.C:
			s.executeCleanup()
		}
	}
}

// runRetry retry定时器执行函数
func (s *CrawlerScheduler) runRetry() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.retryTicker.C:
			s.executeRetry()
		}
	}
}

// executeReurl 执行reurl任务
func (s *CrawlerScheduler) executeReurl() {
	s.mu.Lock()
	if s.reurlProcessing {
		s.mu.Unlock()
		return
	}
	s.reurlProcessing = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.reurlProcessing = false
		s.reurlRuns++
		s.lastReurlTime = time.Now()
		s.mu.Unlock()
	}()

	log.Printf("🖼️ 执行reurl任务")
	s.callEndpoint(fmt.Sprintf("%s/reurl", s.baseURL))
}

// executeRefm 执行refm任务
func (s *CrawlerScheduler) executeRefm() {
	s.mu.Lock()
	if s.refmProcessing {
		s.mu.Unlock()
		return
	}
	s.refmProcessing = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.refmProcessing = false
		s.refmRuns++
		s.lastRefmTime = time.Now()
		s.mu.Unlock()
	}()

	log.Printf("🎨 执行refm任务")
	s.callEndpoint(fmt.Sprintf("%s/refmurl", s.baseURL))
}

// executeImglist 执行imglist任务
func (s *CrawlerScheduler) executeImglist() {
	s.mu.Lock()
	if s.imglistProcessing {
		s.mu.Unlock()
		return
	}
	s.imglistProcessing = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.imglistProcessing = false
		s.imglistRuns++
		s.lastImglistTime = time.Now()
		s.mu.Unlock()
	}()

	log.Printf("📸 执行imglist任务")
	s.callEndpoint(fmt.Sprintf("%s/getimglist", s.baseURL))
}

// executeCleanup 执行cleanup任务
func (s *CrawlerScheduler) executeCleanup() {
	s.mu.Lock()
	if s.cleanupProcessing {
		s.mu.Unlock()
		return
	}
	s.cleanupProcessing = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.cleanupProcessing = false
		s.cleanupRuns++
		s.lastCleanupTime = time.Now()
		s.mu.Unlock()
	}()

	log.Printf("🧹 执行cleanup任务")
	s.callEndpoint(fmt.Sprintf("%s/upnull", s.baseURL))
}

// executeRetry 执行retry任务
func (s *CrawlerScheduler) executeRetry() {
	s.mu.Lock()
	if s.retryProcessing {
		s.mu.Unlock()
		return
	}
	s.retryProcessing = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.retryProcessing = false
		s.retryRuns++
		s.lastRetryTime = time.Now()
		s.mu.Unlock()
	}()

	log.Printf("🔄 执行retry任务")
	s.callEndpoint(fmt.Sprintf("%s/retry", s.baseURL))
}

// callEndpoint 调用HTTP端点
func (s *CrawlerScheduler) callEndpoint(url string) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		log.Printf("❌ 调用端点失败: %s, error: %v", url, err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 读取响应失败: %s, error: %v", url, err)
		return
	}

	if resp.StatusCode == 200 {
		log.Printf("✅ 端点调用成功: %s", url)
		log.Printf("📄 响应: %s", string(body))
	} else {
		log.Printf("⚠️  端点返回错误: %s, status: %d, body: %s", url, resp.StatusCode, string(body))
	}
}
