package repository

import (
	"fmt"
	"strings"
	"xr-gallery/internal/database"
	"xr-gallery/internal/model"

	"gorm.io/gorm"
)

type GalleryRepository struct {
	db *gorm.DB
}

// NewGalleryRepository 创建图库仓库实例
func NewGalleryRepository() *GalleryRepository {
	return &GalleryRepository{
		db: database.GetDB(),
	}
}

// GetGalleryList 获取图库列表
func (r *GalleryRepository) GetGalleryList(page, limit int, sort string) ([]model.GalleryListItem, int64, error) {
	var galleries []model.XR
	var total int64

	// 获取总数 - 只统计xrid大于0的记录
	if err := r.db.Model(&model.XR{}).Where("issave = ? AND xrid > ?", 1, 0).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count galleries: %w", err)
	}

	// 排序逻辑：xrid越大越新，默认降序显示
	orderBy := "xrid DESC"
	if sort == "oldest" {
		orderBy = "xrid ASC"
	}

	// 分页查询 - 使用原生SQL查询，确保xrid不为0
	offset := (page - 1) * limit
	query := fmt.Sprintf(`
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE issave = 1 AND xrid > 0
		ORDER BY %s
		LIMIT %d OFFSET %d
	`, orderBy, limit, offset)

	// 使用原生SQL查询并手动映射结果
	rows, err := r.db.Raw(query).Rows()
	if err != nil {
		return nil, 0, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	galleries = []model.XR{}
	for rows.Next() {
		var gallery model.XR
		if err := rows.Scan(&gallery.ID, &gallery.XRID, &gallery.IsSave, &gallery.FM, &gallery.ReFM, &gallery.Title, &gallery.URL); err != nil {
			return nil, 0, fmt.Errorf("failed to scan row: %w", err)
		}
		galleries = append(galleries, gallery)
	}

	// 转换为响应格式并统计图片数量
	var result []model.GalleryListItem
	for _, gallery := range galleries {
		// 统计该图库的图片数量
		var imageCount int64
		r.db.Model(&model.XRInfo{}).Where("xrid = ?", gallery.XRID).Count(&imageCount)

		item := model.GalleryListItem{
			ID:            gallery.ID,
			XRID:          gallery.XRID,
			Title:         gallery.Title,
			CoverOriginal: gallery.ReFM,
			ImageCount:    int(imageCount),
		}

		// 直接使用原始图片URL（性能更好）
		if gallery.ReFM != "" {
			item.Cover = fmt.Sprintf("https://img1.101616.xyz%s", gallery.ReFM)
		}

		result = append(result, item)
	}

	return result, total, nil
}

// GetGalleryDetail 获取图库详情
func (r *GalleryRepository) GetGalleryDetail(xrid int) (*model.GalleryDetail, error) {
	// 使用原生SQL查询基本信息
	galleryQuery := `
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE xrid = ? AND issave = 1
	`

	rows, err := r.db.Raw(galleryQuery, xrid).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to execute gallery query: %w", err)
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, fmt.Errorf("gallery not found")
	}

	var gallery model.XR
	if err := rows.Scan(&gallery.ID, &gallery.XRID, &gallery.IsSave, &gallery.FM, &gallery.ReFM, &gallery.Title, &gallery.URL); err != nil {
		return nil, fmt.Errorf("failed to scan gallery row: %w", err)
	}

	// 使用原生SQL查询所有图片
	imagesQuery := `
		SELECT id, xrid, ourl, reurl
		FROM xrinfo
		WHERE xrid = ?
		ORDER BY id ASC
	`

	imageRows, err := r.db.Raw(imagesQuery, xrid).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to execute images query: %w", err)
	}
	defer imageRows.Close()

	var images []model.XRInfo
	for imageRows.Next() {
		var img model.XRInfo
		if err := imageRows.Scan(&img.ID, &img.XRID, &img.OURL, &img.ReURL); err != nil {
			return nil, fmt.Errorf("failed to scan image row: %w", err)
		}
		images = append(images, img)
	}

	// 构建图片列表
	var imageItems []model.ImageItem
	for i, img := range images {
		item := model.ImageItem{
			ID:            img.ID,
			OURL:          img.OURL,
			ReURLOriginal: img.ReURL,
			Order:         i + 1,
		}

		// 构建图片代理URL
		if img.ReURL != "" {
			item.ReURL = fmt.Sprintf("https://img1.101616.xyz%s", img.ReURL)
		}

		imageItems = append(imageItems, item)
	}

	// 获取导航信息
	navigation, err := r.GetNavigation(xrid)
	if err != nil {
		return nil, fmt.Errorf("failed to get navigation: %w", err)
	}

	// 构建响应
	info := model.GalleryInfo{
		ID:    gallery.ID,
		XRID:  gallery.XRID,
		Title: gallery.Title,
		URL:   gallery.URL,
	}

	// 构建封面代理URL
	if gallery.ReFM != "" {
		info.Cover = fmt.Sprintf("https://img1.101616.xyz%s", gallery.ReFM)
	}

	detail := &model.GalleryDetail{
		Info:       info,
		Images:     imageItems,
		Navigation: *navigation,
	}

	return detail, nil
}

// GetNavigation 获取导航信息
func (r *GalleryRepository) GetNavigation(currentXrid int) (*model.Navigation, error) {
	var navigation model.Navigation

	// 获取上一套 (xrid更大的，即更新的) - 使用原生SQL
	prevQuery := `
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE xrid > ? AND issave = 1
		ORDER BY xrid ASC
		LIMIT 1
	`

	prevRows, err := r.db.Raw(prevQuery, currentXrid).Rows()
	if err == nil {
		defer prevRows.Close()
		if prevRows.Next() {
			var prevGallery model.XR
			if err := prevRows.Scan(&prevGallery.ID, &prevGallery.XRID, &prevGallery.IsSave, &prevGallery.FM, &prevGallery.ReFM, &prevGallery.Title, &prevGallery.URL); err == nil {
				navigation.Prev = &model.NavigationItem{
					XRID:  prevGallery.XRID,
					Title: prevGallery.Title,
				}
				if prevGallery.ReFM != "" {
					navigation.Prev.Cover = fmt.Sprintf("https://img1.101616.xyz%s", prevGallery.ReFM)
				}
			}
		}
	}

	// 获取下一套 (xrid更小的，即更旧的) - 使用原生SQL
	nextQuery := `
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE xrid < ? AND issave = 1
		ORDER BY xrid DESC
		LIMIT 1
	`

	nextRows, err := r.db.Raw(nextQuery, currentXrid).Rows()
	if err == nil {
		defer nextRows.Close()
		if nextRows.Next() {
			var nextGallery model.XR
			if err := nextRows.Scan(&nextGallery.ID, &nextGallery.XRID, &nextGallery.IsSave, &nextGallery.FM, &nextGallery.ReFM, &nextGallery.Title, &nextGallery.URL); err == nil {
				navigation.Next = &model.NavigationItem{
					XRID:  nextGallery.XRID,
					Title: nextGallery.Title,
				}
				if nextGallery.ReFM != "" {
					navigation.Next.Cover = fmt.Sprintf("https://img1.101616.xyz%s", nextGallery.ReFM)
				}
			}
		}
	}

	return &navigation, nil
}

// CheckXRIDExists 检查xrid是否存在
func (r *GalleryRepository) CheckXRIDExists(xrid int) (bool, error) {
	var count int64
	err := r.db.Model(&model.XR{}).Where("xrid = ?", xrid).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check xrid exists: %w", err)
	}
	return count > 0, nil
}

// UpdateXRTitle 更新xr记录的title字段
func (r *GalleryRepository) UpdateXRTitle(xrid int, title string) error {
	result := r.db.Model(&model.XR{}).Where("xrid = ?", xrid).Update("title", title)
	if result.Error != nil {
		return fmt.Errorf("failed to update title for xrid %d: %w", xrid, result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no record found with xrid %d", xrid)
	}
	return nil
}

// CreateXR 创建新的xr记录
func (r *GalleryRepository) CreateXR(xr *model.XR) error {
	if err := r.db.Create(xr).Error; err != nil {
		return fmt.Errorf("failed to create xr record: %w", err)
	}
	return nil
}

// GetPendingImageRecord 获取待处理的图片记录
func (r *GalleryRepository) GetPendingImageRecord() (*model.XRInfo, error) {
	rows, err := r.db.Raw(`
		SELECT id, xrid, ourl, reurl
		FROM xrinfo
		WHERE (reurl IS NULL OR reurl = '' OR reurl = '4040' OR reurl = 'processing') AND xrid > ?
		ORDER BY reurl ASC, xrid DESC
		LIMIT 1
	`, 16000).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, nil
	}

	var record model.XRInfo
	if err := rows.Scan(&record.ID, &record.XRID, &record.OURL, &record.ReURL); err != nil {
		return nil, err
	}

	return &record, nil
}

// CheckExistingReurl 检查是否已有相同ourl的成功上传记录
func (r *GalleryRepository) CheckExistingReurl(ourl string) (string, error) {
	rows, err := r.db.Raw(`
		SELECT reurl
		FROM xrinfo
		WHERE ourl = ? AND reurl LIKE '/file/%'
		ORDER BY id DESC
		LIMIT 1
	`, ourl).Rows()

	if err != nil {
		return "", err
	}
	defer rows.Close()

	if !rows.Next() {
		return "", nil
	}

	var reurl string
	if err := rows.Scan(&reurl); err != nil {
		return "", err
	}

	return reurl, nil
}

// UpdateImageReurl 更新图片记录的reurl字段
func (r *GalleryRepository) UpdateImageReurl(id int, reurl string) error {
	return r.db.Exec("UPDATE xrinfo SET reurl = ? WHERE id = ?", reurl, id).Error
}

// GetPendingCoverRecord 获取待处理的封面记录
func (r *GalleryRepository) GetPendingCoverRecord() (*model.XR, error) {
	rows, err := r.db.Raw(`
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE (refm IS NULL OR refm = '4040' OR refm = 'processing') AND xrid > ?
		ORDER BY refm ASC, xrid DESC
		LIMIT 1
	`, 16000).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, nil
	}

	var record model.XR
	if err := rows.Scan(&record.ID, &record.XRID, &record.IsSave, &record.FM, &record.ReFM, &record.Title, &record.URL); err != nil {
		return nil, err
	}

	return &record, nil
}

// CheckExistingRefm 检查是否已有相同fm的成功上传记录
func (r *GalleryRepository) CheckExistingRefm(fm string) (string, error) {
	rows, err := r.db.Raw(`
		SELECT refm
		FROM xr
		WHERE fm = ? AND refm LIKE '/file/%'
		ORDER BY id DESC
		LIMIT 1
	`, fm).Rows()

	if err != nil {
		return "", err
	}
	defer rows.Close()

	if !rows.Next() {
		return "", nil
	}

	var refm string
	if err := rows.Scan(&refm); err != nil {
		return "", err
	}

	return refm, nil
}

// UpdateCoverRefm 更新封面记录的refm字段
func (r *GalleryRepository) UpdateCoverRefm(id int, refm string) error {
	return r.db.Exec("UPDATE xr SET refm = ? WHERE id = ?", refm, id).Error
}

// GetPendingDetailRecord 获取待处理的详情页记录
func (r *GalleryRepository) GetPendingDetailRecord() (*model.XR, error) {
	rows, err := r.db.Raw(`
		SELECT id, xrid, issave, title, url
		FROM xr
		WHERE issave = 0 AND xrid > ?
		ORDER BY xrid DESC
		LIMIT 1
	`, 16000).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, nil
	}

	var record model.XR
	if err := rows.Scan(&record.ID, &record.XRID, &record.IsSave, &record.Title, &record.URL); err != nil {
		return nil, err
	}

	return &record, nil
}

// CheckExistingImages 检查已有图片数量
func (r *GalleryRepository) CheckExistingImages(xrid int) (int, int, error) {
	var existingCount, successCount int

	// 查询总数
	err := r.db.Raw("SELECT COUNT(*) FROM xrinfo WHERE xrid = ?", xrid).Scan(&existingCount).Error
	if err != nil {
		return 0, 0, err
	}

	// 查询成功数量（reurl不为空且不是错误状态）
	err = r.db.Raw("SELECT COUNT(*) FROM xrinfo WHERE xrid = ? AND reurl IS NOT NULL AND reurl != '' AND reurl != '4040'", xrid).Scan(&successCount).Error
	if err != nil {
		return existingCount, 0, err
	}

	return existingCount, successCount, nil
}

// CleanExistingImages 清理已有图片记录
func (r *GalleryRepository) CleanExistingImages(xrid int) (int, error) {
	result := r.db.Exec("DELETE FROM xrinfo WHERE xrid = ?", xrid)
	return int(result.RowsAffected), result.Error
}

// UpdateRecordStatus 更新记录状态
func (r *GalleryRepository) UpdateRecordStatus(id int, status int) error {
	return r.db.Exec("UPDATE xr SET issave = ? WHERE id = ?", status, id).Error
}

// BatchCreateImages 批量创建图片记录
func (r *GalleryRepository) BatchCreateImages(xrid int, images []string) (int, error) {
	if len(images) == 0 {
		return 0, nil
	}

	// 构建批量插入SQL
	values := make([]interface{}, 0, len(images)*2)
	placeholders := make([]string, 0, len(images))

	for _, imageURL := range images {
		placeholders = append(placeholders, "(?, ?)")
		values = append(values, xrid, imageURL)
	}

	sql := fmt.Sprintf("INSERT INTO xrinfo (xrid, ourl) VALUES %s", strings.Join(placeholders, ", "))
	result := r.db.Exec(sql, values...)

	return int(result.RowsAffected), result.Error
}
