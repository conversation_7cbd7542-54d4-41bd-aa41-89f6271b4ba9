# 定时器执行流程图

## Reurl 定时器执行流程

```mermaid
graph TD
    A[定时器触发 - 每10秒] --> B{检查处理状态}
    B -->|正在处理| C[跳过本次执行]
    B -->|未处理| D[设置处理状态为true]
    D --> E[调用 /reurl 端点]
    E --> F[获取待处理图片记录]
    F -->|无记录| G[返回 no_records]
    F -->|有记录| H[检查重复上传]
    H -->|已存在| I[复用已上传结果]
    H -->|不存在| J[标记为processing]
    J --> K[构建完整图片URL]
    K --> L[上传图片到Telegraph]
    L -->|成功| M[更新reurl字段]
    L -->|失败| N[标记为4040]
    M --> O[返回成功响应]
    N --> P[返回失败响应]
    I --> O
    G --> Q[重置处理状态]
    O --> Q
    P --> Q
    C --> R[等待下次触发]
    Q --> R
```

## Refm 定时器执行流程

```mermaid
graph TD
    A[定时器触发 - 每30秒] --> B{检查处理状态}
    B -->|正在处理| C[跳过本次执行]
    B -->|未处理| D[设置处理状态为true]
    D --> E[调用 /refmurl 端点]
    E --> F[获取待处理封面记录]
    F -->|无记录| G[返回 no_records]
    F -->|有记录| H[检查重复上传]
    H -->|已存在| I[复用已上传结果]
    H -->|不存在| J[标记为processing]
    J --> K[构建完整封面URL]
    K --> L[上传封面到Telegraph]
    L -->|成功| M[更新refm字段]
    L -->|失败| N[标记为4040]
    M --> O[返回成功响应]
    N --> P[返回失败响应]
    I --> O
    G --> Q[重置处理状态]
    O --> Q
    P --> Q
    C --> R[等待下次触发]
    Q --> R
```

## 数据库查询流程

### Reurl 数据库操作

```mermaid
graph LR
    A[GetPendingImageRecord] --> B[SELECT from xrinfo]
    B --> C{WHERE条件}
    C --> D[reurl IS NULL OR reurl = '' OR reurl = '4040' OR reurl = 'processing']
    D --> E[AND xrid > 16000]
    E --> F[ORDER BY reurl ASC, xrid DESC]
    F --> G[LIMIT 1]
    
    H[CheckExistingReurl] --> I[SELECT reurl from xrinfo]
    I --> J[WHERE ourl = ? AND reurl LIKE '/file/%']
    J --> K[ORDER BY id DESC LIMIT 1]
    
    L[UpdateImageReurl] --> M[UPDATE xrinfo SET reurl = ? WHERE id = ?]
```

### Refm 数据库操作

```mermaid
graph LR
    A[GetPendingCoverRecord] --> B[SELECT from xr]
    B --> C{WHERE条件}
    C --> D[refm IS NULL OR refm = '4040' OR refm = 'processing']
    D --> E[AND xrid > 16000]
    E --> F[ORDER BY refm ASC, xrid DESC]
    F --> G[LIMIT 1]
    
    H[CheckExistingRefm] --> I[SELECT refm from xr]
    I --> J[WHERE fm = ? AND refm LIKE '/file/%']
    J --> K[ORDER BY id DESC LIMIT 1]
    
    L[UpdateCoverRefm] --> M[UPDATE xr SET refm = ? WHERE id = ?]
```

## 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> Idle: 初始状态
    Idle --> Checking: 定时器触发
    Checking --> Idle: 正在处理中，跳过
    Checking --> Processing: 开始处理
    Processing --> HTTPCall: 调用HTTP端点
    HTTPCall --> DBQuery: 查询数据库
    DBQuery --> ImageUpload: 上传图片
    ImageUpload --> DBUpdate: 更新数据库
    DBUpdate --> Idle: 处理完成
    ImageUpload --> Idle: 上传失败
    DBQuery --> Idle: 无待处理记录
```

## 错误处理流程

```mermaid
graph TD
    A[开始处理] --> B{数据库连接}
    B -->|失败| C[返回数据库错误]
    B -->|成功| D{获取记录}
    D -->|失败| E[返回查询错误]
    D -->|无记录| F[返回no_records]
    D -->|成功| G{URL构建}
    G -->|失败| H[标记4040状态]
    G -->|成功| I{图片上传}
    I -->|失败| J[标记4040状态]
    I -->|成功| K{数据库更新}
    K -->|失败| L[记录更新错误]
    K -->|成功| M[返回成功]
    
    C --> N[重置处理状态]
    E --> N
    F --> N
    H --> N
    J --> N
    L --> N
    M --> N
    N --> O[结束]
```

## 并发控制机制

### Rust版本
```rust
// 使用异步锁
let mut states_guard = states.write().await;
if states_guard.is_processing_reurl {
    return; // 防重复执行
}
states_guard.is_processing_reurl = true;
```

### Go版本
```go
// 使用互斥锁
s.mu.Lock()
if s.reurlProcessing {
    s.mu.Unlock()
    return // 防重复执行
}
s.reurlProcessing = true
s.mu.Unlock()
```

## 关键时间节点

| 操作 | Rust版本 | Go版本 | 说明 |
|------|----------|--------|------|
| Reurl执行间隔 | 10秒 | 10秒 | ✅ 一致 |
| Refm执行间隔 | 30秒 | 30秒 | ✅ 一致 |
| HTTP超时 | 30秒 | 30秒 | ✅ 一致 |
| 数据库查询超时 | 默认 | 默认 | ✅ 一致 |

## 性能指标对比

| 指标 | Rust版本 | Go版本 | 备注 |
|------|----------|--------|------|
| 内存使用 | 低 | 中等 | Rust零拷贝优势 |
| CPU使用 | 低 | 低 | 业务逻辑简单 |
| 并发处理 | 异步 | Goroutine | 都支持高并发 |
| 启动时间 | 快 | 快 | 都很快 |
| 错误恢复 | 自动 | 自动 | 都有重试机制 |

## 监控指标

### Go版本额外提供的监控指标

```json
{
  "reurl_timer": {
    "interval": "10s",
    "runs": 1234,
    "processing": false,
    "last_run": "2024-01-15 10:30:45",
    "next_run": "2024-01-15 10:30:55"
  },
  "refm_timer": {
    "interval": "30s", 
    "runs": 456,
    "processing": false,
    "last_run": "2024-01-15 10:30:30",
    "next_run": "2024-01-15 10:31:00"
  }
}
```

## 总结

两个版本的执行流程在核心逻辑上完全一致：

1. **定时触发机制**: 时间间隔相同
2. **状态管理**: 都有防重复执行机制  
3. **数据库操作**: SQL语句逻辑相同
4. **错误处理**: 策略一致
5. **HTTP调用**: 端点和参数相同

Go版本在监控和日志方面有所增强，但不影响核心功能的一致性。
