#!/bin/bash

# XR Backend 部署脚本
# 使用方法: ./deploy.sh [环境] [架构]
# 环境: dev/prod (默认: prod)
# 架构: amd64/arm64 (默认: amd64)

set -e

# 配置变量
ENVIRONMENT=${1:-prod}
ARCH=${2:-amd64}
PROJECT_NAME="xr-backend"
REMOTE_HOST="*************"
REMOTE_USER="root"
REMOTE_PORT="22"
REMOTE_PATH="/root/xr/backend"
LOCAL_DIST_PATH="./dist"
CONFIG_PATH="./config"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查必要文件
check_files() {
    log_info "检查部署文件..."
    
    if [ ! -f "${LOCAL_DIST_PATH}/main_linux_${ARCH}" ]; then
        log_error "可执行文件不存在: ${LOCAL_DIST_PATH}/main_linux_${ARCH}"
        log_info "请先运行编译脚本: ./build_and_run.ps1"
        exit 1
    fi
    
    if [ ! -f "${CONFIG_PATH}/config.yaml" ]; then
        log_error "配置文件不存在: ${CONFIG_PATH}/config.yaml"
        exit 1
    fi
    
    if [ ! -f "finalUrl.txt" ]; then
        log_warning "finalUrl.txt 不存在，将使用默认值"
        echo "www.xiu01.top" > finalUrl.txt
    fi
    
    log_success "文件检查完成"
}

# 创建远程目录
create_remote_dirs() {
    log_info "创建远程目录..."
    
    ssh -p ${REMOTE_PORT} ${REMOTE_USER}@${REMOTE_HOST} "
        mkdir -p ${REMOTE_PATH}
        mkdir -p ${REMOTE_PATH}/config
        mkdir -p /logs
        mkdir -p /etc/supervisor/conf.d
    "
    
    log_success "远程目录创建完成"
}

# 上传文件
upload_files() {
    log_info "上传文件到服务器..."
    
    # 上传可执行文件
    scp -P ${REMOTE_PORT} "${LOCAL_DIST_PATH}/main_linux_${ARCH}" \
        ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/main_linux_${ARCH}
    
    # 上传配置文件
    scp -P ${REMOTE_PORT} "${CONFIG_PATH}/config.yaml" \
        ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/config/config.yaml
    
    # 上传finalUrl.txt
    scp -P ${REMOTE_PORT} "finalUrl.txt" \
        ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/finalUrl.txt
    
    # 上传supervisor配置
    scp -P ${REMOTE_PORT} "deploy/supervisor.conf" \
        ${REMOTE_USER}@${REMOTE_HOST}:/etc/supervisor/conf.d/xr-backend.conf
    
    log_success "文件上传完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    ssh -p ${REMOTE_PORT} ${REMOTE_USER}@${REMOTE_HOST} "
        chmod +x ${REMOTE_PATH}/main_linux_${ARCH}
        chown -R root:root ${REMOTE_PATH}
    "
    
    log_success "权限设置完成"
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    
    ssh -p ${REMOTE_PORT} ${REMOTE_USER}@${REMOTE_HOST} "
        # 重新加载supervisor配置
        supervisorctl reread
        supervisorctl update
        
        # 停止旧服务（如果存在）
        supervisorctl stop xr-backend || true
        
        # 等待服务完全停止
        sleep 3
        
        # 启动新服务
        supervisorctl start xr-backend
        
        # 检查服务状态
        supervisorctl status xr-backend
    "
    
    log_success "服务重启完成"
}

# 检查服务状态
check_service() {
    log_info "检查服务状态..."
    
    sleep 5
    
    # 检查进程
    ssh -p ${REMOTE_PORT} ${REMOTE_USER}@${REMOTE_HOST} "
        echo '=== 进程状态 ==='
        ps aux | grep main_linux_${ARCH} | grep -v grep || echo '进程未找到'
        
        echo '=== Supervisor状态 ==='
        supervisorctl status xr-backend
        
        echo '=== 端口监听 ==='
        netstat -tlnp | grep :3332 || echo '端口3332未监听'
        
        echo '=== 最新日志 ==='
        tail -n 10 /logs/xr-backend.log || echo '日志文件不存在'
    "
}

# 测试API
test_api() {
    log_info "测试API连接..."
    
    sleep 3
    
    # 测试健康检查
    if curl -s -f "http://${REMOTE_HOST}:3332/ping" > /dev/null; then
        log_success "API健康检查通过"
    else
        log_warning "API健康检查失败，请检查服务状态"
    fi
    
    # 测试调度器状态
    if curl -s -f "http://${REMOTE_HOST}:3332/scheduler/status" > /dev/null; then
        log_success "调度器API正常"
    else
        log_warning "调度器API异常"
    fi
}

# 主函数
main() {
    log_info "开始部署 ${PROJECT_NAME} (环境: ${ENVIRONMENT}, 架构: ${ARCH})"
    
    check_files
    create_remote_dirs
    upload_files
    set_permissions
    restart_service
    check_service
    test_api
    
    log_success "部署完成！"
    log_info "服务地址: http://${REMOTE_HOST}:3332"
    log_info "调度器状态: http://${REMOTE_HOST}:3332/scheduler/status"
    log_info "查看日志: ssh ${REMOTE_USER}@${REMOTE_HOST} 'tail -f /logs/xr-backend.log'"
}

# 执行主函数
main "$@"
