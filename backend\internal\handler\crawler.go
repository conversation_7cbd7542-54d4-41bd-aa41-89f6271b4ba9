package handler

import (
	"context"
	"log"
	"strconv"
	"xr-gallery/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// GetList 获取列表页数据并保存到数据库
func GetList(ctx context.Context, c *app.RequestContext) {
	log.Printf("📋 开始处理getlist请求")

	// 获取查询参数
	pageStr := c.<PERSON><PERSON>ult<PERSON>("page", "1")

	// 参数验证和转换
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	log.Printf("🎯 处理第%d页列表数据", page)

	// 创建爬虫服务实例
	crawlerService := service.NewCrawlerService()

	// 爬取列表页数据
	items, err := crawlerService.GetListPage(page)
	if err != nil {
		log.Printf("❌ 爬取第%d页失败: %v", page, err)
		c.<PERSON><PERSON>("Content-Type", "application/json; charset=utf-8")
		c.J<PERSON>(500, utils.H{
			"code":    500,
			"message": "爬取数据失败",
			"error":   err.Error(),
		})
		return
	}

	if len(items) == 0 {
		log.Printf("⚠️  第%d页未获取到任何数据", page)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"code":    200,
			"message": "success",
			"data": utils.H{
				"page":         page,
				"items_count":  0,
				"insert_count": 0,
				"update_count": 0,
				"items":        []interface{}{},
			},
		})
		return
	}

	// 保存或更新数据到数据库
	insertCount, updateCount, err := crawlerService.SaveOrUpdateItems(items)
	if err != nil {
		log.Printf("❌ 保存数据失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "保存数据失败",
			"error":   err.Error(),
		})
		return
	}

	log.Printf("✅ 第%d页处理完成: 爬取%d条, 插入%d条, 更新%d条", page, len(items), insertCount, updateCount)

	// 返回成功响应
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "success",
		"data": utils.H{
			"page":         page,
			"items_count":  len(items),
			"insert_count": insertCount,
			"update_count": updateCount,
			"items":        items,
		},
	})
}
