package handler

import (
	"context"
	"fmt"
	"log"
	"xr-gallery/internal/repository"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// Upnull 数据清理处理器
func Upnull(ctx context.Context, c *app.RequestContext) {
	log.Printf("🧹 开始清理无效数据")

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 执行数据清理
	clearedReurl, resetRecords, err := repo.CleanupInvalidData()
	if err != nil {
		log.Printf("❌ 数据清理失败: %v", err)
		c.<PERSON>er("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("数据清理失败: %v", err),
		})
		return
	}

	log.Printf("🧹 数据清理完成: 清理%d条reurl, 重置%d条记录", clearedReurl, resetRecords)

	c.<PERSON><PERSON>("Content-Type", "application/json; charset=utf-8")
	c.<PERSON>(200, utils.H{
		"success":      true,
		"clearedReurl": clearedReurl,
		"resetRecords": resetRecords,
		"message":      fmt.Sprintf("清理%d条reurl, 重置%d条记录", clearedReurl, resetRecords),
	})
}
