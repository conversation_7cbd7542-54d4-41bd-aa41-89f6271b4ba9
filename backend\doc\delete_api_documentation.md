# 删除API文档

## API概述

新增的删除API端点用于删除指定xrid的所有相关数据并重置状态，确保数据的完整性和一致性。

## 端点信息

### DELETE /del/{xrid}

删除指定xrid的所有xrinfo表记录，并将xr表中对应记录的issave状态重置为0。

#### 请求参数

| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| xrid | int | 路径参数 | 是 | 要删除的记录的xrid值，必须为正整数 |

#### 请求示例

```bash
# 删除xrid=17740的所有数据
curl -X DELETE "http://localhost:3332/del/17740" \
     -H "Content-Type: application/json"

# 删除xrid=12345的所有数据
curl -X DELETE "http://localhost:3332/del/12345" \
     -H "Content-Type: application/json"
```

#### 响应格式

##### 成功响应 (200 OK)

```json
{
  "success": true,
  "xrid": 17740,
  "deleted_count": 79,
  "message": "成功删除xrid=17740的79条记录并重置状态",
  "operations": [
    "删除xrinfo表中79条记录",
    "重置xr表中issave状态为0"
  ]
}
```

**响应字段说明:**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 操作是否成功 |
| xrid | int | 被删除的xrid值 |
| deleted_count | int | 从xrinfo表中删除的记录数量 |
| message | string | 操作结果描述信息 |
| operations | array | 执行的操作列表 |

##### 错误响应

**400 Bad Request - 缺少参数**
```json
{
  "success": false,
  "error": "缺少xrid参数"
}
```

**400 Bad Request - 无效参数**
```json
{
  "success": false,
  "error": "无效的xrid参数: abc"
}
```

**500 Internal Server Error - 数据库错误**
```json
{
  "success": false,
  "error": "删除数据失败: database connection error",
  "xrid": 17740
}
```

## 操作详情

### 执行的数据库操作

1. **删除xrinfo表记录**
   ```sql
   DELETE FROM xrinfo WHERE xrid = ?
   ```

2. **重置xr表状态**
   ```sql
   UPDATE xr SET issave = 0 WHERE xrid = ?
   ```

### 事务处理

- 使用数据库事务确保操作的原子性
- 如果任一操作失败，整个事务回滚
- 确保数据一致性

### 日志记录

操作过程中会记录详细日志：

```
🗑️ 开始删除xrid=17740的所有数据
✅ 删除完成: xrid=17740, 删除了79条xrinfo记录, 重置了xr表状态
```

## 使用场景

### 1. 数据清理
当某个图库的图片数据出现问题时，可以使用此API清理所有相关数据：
```bash
curl -X DELETE "http://localhost:3332/del/17740"
```

### 2. 重新处理
删除数据后，该xrid的issave状态被重置为0，可以重新被定时器处理：
- imglist定时器会重新爬取该图库的图片
- reurl定时器会重新上传图片
- refm定时器会重新处理封面

### 3. 批量清理
可以通过脚本批量清理多个xrid：
```bash
#!/bin/bash
xrids=(17740 17741 17742 17743)
for xrid in "${xrids[@]}"; do
    echo "删除xrid: $xrid"
    curl -X DELETE "http://localhost:3332/del/$xrid"
    echo ""
done
```

## 安全考虑

### 1. 参数验证
- 严格验证xrid参数，必须为正整数
- 防止SQL注入攻击

### 2. 操作确认
- 删除操作不可逆，建议在生产环境中添加确认机制
- 可以考虑添加软删除功能

### 3. 权限控制
- 建议在生产环境中添加身份验证
- 限制删除操作的访问权限

## 性能考虑

### 1. 批量删除
- 对于大量数据的删除，操作可能耗时较长
- 建议在低峰期执行

### 2. 索引影响
- 删除操作会影响相关索引
- xrid字段上的索引有助于提高删除性能

### 3. 事务大小
- 大量数据删除时，事务可能较大
- 可以考虑分批删除优化

## 监控和告警

### 1. 操作监控
- 监控删除操作的执行时间
- 记录删除的数据量

### 2. 错误告警
- 删除失败时发送告警
- 监控异常的删除模式

## 测试用例

### 1. 正常删除
```bash
# 测试删除存在的xrid
curl -X DELETE "http://localhost:3332/del/17740"
# 预期: 返回成功，deleted_count > 0
```

### 2. 删除不存在的xrid
```bash
# 测试删除不存在的xrid
curl -X DELETE "http://localhost:3332/del/999999"
# 预期: 返回成功，deleted_count = 0
```

### 3. 无效参数
```bash
# 测试无效参数
curl -X DELETE "http://localhost:3332/del/abc"
# 预期: 返回400错误
```

### 4. 缺少参数
```bash
# 测试缺少参数
curl -X DELETE "http://localhost:3332/del/"
# 预期: 返回404错误（路由不匹配）
```

## 与现有系统集成

### 1. 定时器系统
删除操作后，相关定时器会自动处理重置的数据：
- **imglist定时器**: 重新爬取图片列表
- **reurl定时器**: 重新上传内页图片
- **refm定时器**: 重新上传封面图片

### 2. 状态流转
```
删除前: xr.issave = 1 (已完成)
删除后: xr.issave = 0 (未处理)
处理后: xr.issave = 1 (重新完成)
```

### 3. 数据恢复
删除操作不可逆，但可以通过以下方式恢复数据：
1. 等待定时器重新处理
2. 手动调用相关API重新处理
3. 从备份中恢复（如果有）

## 总结

删除API提供了一个安全、可靠的方式来清理指定xrid的所有相关数据，并确保系统状态的一致性。通过事务处理和详细的日志记录，确保操作的可靠性和可追溯性。
