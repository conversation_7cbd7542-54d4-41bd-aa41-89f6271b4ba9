# XR Backend 开发热加载启动脚本
# 使用 Air 工具实现热加载，代码修改后自动重新编译和重启

param(
    [string]$Mode = "dev"  # 运行模式: dev/debug
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[信息] $Message" "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[成功] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[警告] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[错误] $Message" "Red"
}

# 检查Air是否安装
function Test-AirInstalled {
    try {
        $airVersion = air -v 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Air 已安装: $airVersion"
            return $true
        }
    }
    catch {
        # 忽略错误
    }
    
    Write-Warning "Air 未安装，正在安装..."
    return $false
}

# 安装Air
function Install-Air {
    Write-Info "正在安装 Air 热加载工具..."
    
    try {
        go install github.com/air-verse/air@latest
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Air 安装成功"
            return $true
        }
        else {
            Write-Error "Air 安装失败"
            return $false
        }
    }
    catch {
        Write-Error "Air 安装过程中出现异常: $_"
        return $false
    }
}

# 检查配置文件
function Test-ConfigFiles {
    Write-Info "检查配置文件..."
    
    $configFiles = @(
        "config/config.yaml",
        ".air.toml"
    )
    
    $allExists = $true
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            Write-Success "✓ $file"
        }
        else {
            Write-Warning "✗ $file 不存在"
            $allExists = $false
        }
    }
    
    # 检查finalUrl.txt，如果不存在则创建
    if (-not (Test-Path "finalUrl.txt")) {
        Write-Warning "finalUrl.txt 不存在，创建默认文件"
        "www.xiu01.top" | Out-File -FilePath "finalUrl.txt" -Encoding UTF8
        Write-Success "✓ finalUrl.txt (已创建)"
    }
    else {
        Write-Success "✓ finalUrl.txt"
    }
    
    return $allExists
}

# 清理临时文件
function Clear-TempFiles {
    Write-Info "清理临时文件..."
    
    $tempDirs = @("tmp", "dist")
    foreach ($dir in $tempDirs) {
        if (Test-Path $dir) {
            Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
            Write-Success "已清理: $dir"
        }
    }
    
    # 清理日志文件
    if (Test-Path "build-errors.log") {
        Remove-Item -Path "build-errors.log" -Force -ErrorAction SilentlyContinue
        Write-Success "已清理: build-errors.log"
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host ""
    Write-ColorOutput "🚀 XR Backend 热加载开发环境" "Magenta"
    Write-Host ""
    Write-ColorOutput "功能特性:" "Yellow"
    Write-Host "  • 🔥 代码热加载 - 修改代码自动重新编译"
    Write-Host "  • 📊 实时日志 - 彩色输出，易于调试"
    Write-Host "  • 🛡️ 错误处理 - 编译错误不会中断服务"
    Write-Host "  • ⚡ 快速重启 - 1秒内完成重新加载"
    Write-Host ""
    Write-ColorOutput "使用方法:" "Yellow"
    Write-Host "  .\dev.ps1          # 启动开发环境"
    Write-Host "  .\dev.ps1 debug    # 启动调试模式"
    Write-Host ""
    Write-ColorOutput "快捷键:" "Yellow"
    Write-Host "  Ctrl+C             # 停止服务"
    Write-Host "  r + Enter          # 手动重启"
    Write-Host ""
    Write-ColorOutput "API地址:" "Yellow"
    Write-Host "  http://127.0.0.1:3332/ping                    # 健康检查"
    Write-Host "  http://127.0.0.1:3332/api/gallery/list        # 图库列表"
    Write-Host "  http://127.0.0.1:3332/scheduler/status        # 调度器状态"
    Write-Host ""
}

# 主函数
function Main {
    Write-Host ""
    Write-ColorOutput "XR Backend 热加载开发环境启动" "Magenta"
    Write-Host ""
    
    # 显示帮助信息
    Show-Help
    
    # 检查Air是否安装
    if (-not (Test-AirInstalled)) {
        if (-not (Install-Air)) {
            Write-Error "无法安装 Air，请手动安装后重试"
            Write-Info "手动安装命令: go install github.com/air-verse/air@latest"
            exit 1
        }
    }
    
    # 检查配置文件
    if (-not (Test-ConfigFiles)) {
        Write-Warning "部分配置文件缺失，但将继续启动"
    }
    
    # 清理临时文件
    Clear-TempFiles
    
    # 设置环境变量
    $env:GIN_MODE = if ($Mode -eq "debug") { "debug" } else { "release" }
    
    Write-Info "运行模式: $Mode"
    Write-Info "GIN模式: $($env:GIN_MODE)"
    Write-Success "开发环境配置完成"
    Write-Host ""
    
    Write-ColorOutput "🔥 启动热加载服务..." "Green"
    Write-ColorOutput "📝 修改代码将自动触发重新编译" "Cyan"
    Write-ColorOutput "🌐 服务地址: http://127.0.0.1:3332" "Yellow"
    Write-Host ""
    
    # 启动Air
    try {
        air -d
    }
    catch {
        Write-Error "Air 启动失败: $_"
        exit 1
    }
    finally {
        Write-Info "正在清理..."
        Clear-TempFiles
        Write-Success "开发环境已停止"
    }
}

# 执行主函数
Main
