package service

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
	"xr-gallery/internal/model"
	"xr-gallery/internal/repository"

	"github.com/PuerkitoBio/goquery"
)

// CrawlerService 爬虫服务
type CrawlerService struct {
	client     *http.Client
	userAgent  string
	repo       *repository.GalleryRepository
	urlManager *URLManager
}

// NewCrawlerService 创建爬虫服务实例
func NewCrawlerService() *CrawlerService {
	return &CrawlerService{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		repo:       repository.NewGalleryRepository(),
		urlManager: NewURLManager(),
	}
}

// ListItem 列表项数据结构
type ListItem struct {
	XRID  int    `json:"xrid"`
	FM    string `json:"fm"`
	Title string `json:"title"`
	URL   string `json:"url"`
}

// GetListPage 获取列表页数据
func (s *CrawlerService) GetListPage(page int) ([]ListItem, error) {
	log.Printf("🎯 开始爬取第%d页列表数据", page)

	// 构建URL
	var path string
	if page == 1 {
		path = "XiuRen/"
	} else {
		path = fmt.Sprintf("XiuRen/index%d.html", page)
	}

	pageURL, err := s.urlManager.BuildProxyURL(path)
	if err != nil {
		return nil, fmt.Errorf("构建URL失败: %w", err)
	}

	log.Printf("📡 请求URL: %s", pageURL)

	// 创建请求
	req, err := http.NewRequest("GET", pageURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", s.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3")
	req.Header.Set("Accept-Encoding", "identity") // 不使用压缩，避免解压问题
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	log.Printf("📊 HTML内容长度: %d 字节", len(body))

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	// 调试：检查页面结构
	iListCount := doc.Find(".i_list").Length()
	listN2Count := doc.Find(".list_n2").Length()
	combinedCount := doc.Find(".i_list.list_n2").Length()

	log.Printf("🔍 找到的 .i_list 元素数量: %d", iListCount)
	log.Printf("🔍 找到的 .list_n2 元素数量: %d", listN2Count)
	log.Printf("🔍 找到的 .i_list.list_n2 元素数量: %d", combinedCount)

	// 显示HTML内容预览
	htmlPreview := string(body)
	if len(htmlPreview) > 500 {
		htmlPreview = htmlPreview[:500] + "..."
	}
	log.Printf("📄 HTML内容预览: %s", htmlPreview)

	// 提取列表项
	var items []ListItem
	doc.Find(".i_list.list_n2").Each(func(i int, s *goquery.Selection) {
		item, err := extractListItem(s, i+1)
		if err != nil {
			log.Printf("⚠️  提取第%d项失败: %v", i+1, err)
			return
		}
		if item != nil {
			log.Printf("✅ 提取成功 #%d: xrid=%d, title=%s", i+1, item.XRID, item.Title)
			items = append(items, *item)
		}
	})

	log.Printf("🎉 第%d页爬取完成: 提取%d条有效记录", page, len(items))

	return items, nil
}

// extractListItem 提取单个列表项数据
func extractListItem(s *goquery.Selection, index int) (*ListItem, error) {
	// 查找图片元素
	img := s.Find("img").First()
	if img.Length() == 0 {
		return nil, fmt.Errorf("未找到img元素")
	}

	// 获取图片URL - 优先data-original，备选src
	fm, exists := img.Attr("data-original")
	if !exists {
		fm, exists = img.Attr("src")
		if !exists {
			return nil, fmt.Errorf("未找到图片URL")
		}
	}

	// 查找链接元素
	a := s.Find("a").First()
	if a.Length() == 0 {
		return nil, fmt.Errorf("未找到a元素")
	}

	href, exists := a.Attr("href")
	if !exists {
		return nil, fmt.Errorf("未找到href属性")
	}

	// 查找标题元素
	titleElement := s.Find(".list_n2_title, .meta-title").First()
	if titleElement.Length() == 0 {
		return nil, fmt.Errorf("未找到标题元素")
	}

	title := strings.TrimSpace(titleElement.Text())
	if title == "" {
		return nil, fmt.Errorf("标题为空")
	}

	// 提取xrid
	xrid := extractXRIDFromURL(fm)
	if xrid == 0 {
		xrid = extractXRIDFromURL(href)
	}
	if xrid == 0 {
		return nil, fmt.Errorf("无法提取xrid")
	}

	return &ListItem{
		XRID:  xrid,
		FM:    fm,
		Title: title,
		URL:   href,
	}, nil
}

// extractXRIDFromURL 从URL中提取xrid
func extractXRIDFromURL(url string) int {
	// 匹配数字模式
	re := regexp.MustCompile(`(\d+)`)
	matches := re.FindAllString(url, -1)

	// 查找最可能的xrid（通常是较大的数字）
	for i := len(matches) - 1; i >= 0; i-- {
		if num, err := strconv.Atoi(matches[i]); err == nil && num > 1000 {
			return num
		}
	}

	return 0
}

// SaveOrUpdateItems 保存或更新列表项到数据库
func (s *CrawlerService) SaveOrUpdateItems(items []ListItem) (int, int, error) {
	var insertCount, updateCount int

	for _, item := range items {
		// 检查xrid是否存在
		exists, err := s.repo.CheckXRIDExists(item.XRID)
		if err != nil {
			log.Printf("❌ 检查xrid %d 是否存在失败: %v", item.XRID, err)
			continue
		}

		if exists {
			// 更新title字段
			err = s.repo.UpdateXRTitle(item.XRID, item.Title)
			if err != nil {
				log.Printf("❌ 更新xrid %d 的title失败: %v", item.XRID, err)
				continue
			}
			updateCount++
			log.Printf("🔄 更新记录: xrid=%d, title=%s", item.XRID, item.Title)
		} else {
			// 插入新记录
			xr := &model.XR{
				XRID:   item.XRID,
				IsSave: 0, // 默认未保存
				FM:     item.FM,
				ReFM:   "", // 初始为空
				Title:  item.Title,
				URL:    item.URL,
			}

			err = s.repo.CreateXR(xr)
			if err != nil {
				log.Printf("❌ 插入xrid %d 失败: %v", item.XRID, err)
				continue
			}
			insertCount++
			log.Printf("➕ 插入新记录: xrid=%d, title=%s", item.XRID, item.Title)
		}
	}

	return insertCount, updateCount, nil
}
