package service

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
)

// URLManager URL管理服务
type URLManager struct {
	client      *http.Client
	proxyPrefix string
	finalUrlFile string
}

// NewURLManager 创建URL管理器实例
func NewURLManager() *URLManager {
	return &URLManager{
		client: &http.Client{
			Timeout: 15 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// 不跟随重定向，我们要手动处理
				return http.ErrUseLastResponse
			},
		},
		proxyPrefix:  "https://re.101616.xyz/",
		finalUrlFile: "finalUrl.txt",
	}
}

// GetBaseURL 获取基础URL
func (u *URLManager) GetBaseURL() (string, error) {
	// 尝试从文件读取
	if content, err := os.ReadFile(u.finalUrlFile); err == nil {
		url := strings.TrimSpace(string(content))
		if url != "" {
			log.Printf("📁 从 %s 读取基础URL: %s", u.finalUrlFile, url)
			return url, nil
		}
	}

	// 使用默认URL
	defaultURL := "www.xiu01.top"
	log.Printf("⚠️  使用默认基础URL: %s", defaultURL)
	
	// 创建默认文件
	if err := u.updateFinalURL(defaultURL); err != nil {
		log.Printf("❌ 创建默认URL文件失败: %v", err)
	}
	
	return defaultURL, nil
}

// BuildProxyURL 构建代理URL
func (u *URLManager) BuildProxyURL(path string) (string, error) {
	baseURL, err := u.GetBaseURL()
	if err != nil {
		return "", err
	}

	// 清理baseURL，移除协议前缀
	cleanBaseURL := strings.TrimPrefix(baseURL, "http://")
	cleanBaseURL = strings.TrimPrefix(cleanBaseURL, "https://")
	cleanBaseURL = strings.TrimSuffix(cleanBaseURL, "/")

	// 构建完整URL
	fullURL := fmt.Sprintf("%s%s/%s", u.proxyPrefix, cleanBaseURL, path)
	return fullURL, nil
}

// CheckAndUpdateBaseURL 检查并更新基础URL
func (u *URLManager) CheckAndUpdateBaseURL() error {
	log.Printf("🔄 开始检查域名状态")

	baseURL, err := u.GetBaseURL()
	if err != nil {
		return fmt.Errorf("获取基础URL失败: %w", err)
	}

	// 构建测试URL
	testURL := baseURL
	if !strings.HasPrefix(testURL, "http") {
		testURL = "https://" + testURL
	}

	log.Printf("🌐 检查URL: %s", testURL)

	// 发送请求检查状态
	resp, err := u.client.Get(testURL)
	if err != nil {
		log.Printf("⚠️  域名检查失败: %s - %v", baseURL, err)
		return fmt.Errorf("域名检查失败: %w", err)
	}
	defer resp.Body.Close()

	status := resp.StatusCode
	log.Printf("📊 HTTP状态码: %d", status)

	// 检查是否有重定向
	if status >= 300 && status < 400 {
		location := resp.Header.Get("Location")
		if location != "" && location != baseURL {
			log.Printf("🔄 发现重定向: %s -> %s", baseURL, location)
			
			// 清理新URL
			newURL := strings.TrimPrefix(location, "http://")
			newURL = strings.TrimPrefix(newURL, "https://")
			newURL = strings.TrimSuffix(newURL, "/")
			
			if newURL != baseURL {
				// 更新文件中的URL
				if err := u.updateFinalURL(newURL); err != nil {
					log.Printf("❌ 更新URL文件失败: %v", err)
					return fmt.Errorf("更新URL文件失败: %w", err)
				}
				log.Printf("🎉 域名已更新: %s -> %s", baseURL, newURL)
			} else {
				log.Printf("✅ 域名无变化: %s", baseURL)
			}
		}
	} else if status >= 200 && status < 300 {
		log.Printf("✅ 域名检查成功: %s", baseURL)
	} else {
		log.Printf("⚠️  域名响应异常: %s - HTTP %d", baseURL, status)
		return fmt.Errorf("域名响应异常: HTTP %d", status)
	}

	return nil
}

// updateFinalURL 更新finalUrl.txt文件
func (u *URLManager) updateFinalURL(newURL string) error {
	// 确保URL格式正确
	cleanURL := strings.TrimSpace(newURL)
	cleanURL = strings.TrimPrefix(cleanURL, "http://")
	cleanURL = strings.TrimPrefix(cleanURL, "https://")
	cleanURL = strings.TrimSuffix(cleanURL, "/")

	// 写入文件
	err := os.WriteFile(u.finalUrlFile, []byte(cleanURL), 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	log.Printf("📝 已更新 %s: %s", u.finalUrlFile, cleanURL)
	return nil
}

// GetCurrentURL 获取当前使用的完整URL（用于调试）
func (u *URLManager) GetCurrentURL() (string, error) {
	return u.BuildProxyURL("XiuRen/")
}

// TestConnection 测试连接
func (u *URLManager) TestConnection() error {
	testURL, err := u.GetCurrentURL()
	if err != nil {
		return fmt.Errorf("构建测试URL失败: %w", err)
	}

	log.Printf("🧪 测试连接: %s", testURL)

	req, err := http.NewRequest("GET", testURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP请求失败: %d", resp.StatusCode)
	}

	// 读取少量内容验证
	body, err := io.ReadAll(io.LimitReader(resp.Body, 1024))
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	log.Printf("✅ 连接测试成功，响应长度: %d 字节", len(body))
	return nil
}
