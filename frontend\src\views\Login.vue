<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <!-- 登录标题 -->
        <div class="login-header">
          <h1 class="login-title">XR Gallery</h1>
          <p class="login-subtitle">用户登录</p>
        </div>

        <!-- 登录表单 -->
        <n-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          size="large"
          :show-label="false"
        >
          <n-form-item path="username">
            <n-input
              v-model:value="formData.username"
              placeholder="请输入用户名"
              :disabled="authStore.loading"
              @keydown.enter="handleLogin"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="password">
            <n-input
              v-model:value="formData.password"
              type="password"
              placeholder="请输入密码"
              :disabled="authStore.loading"
              show-password-on="mousedown"
              @keydown.enter="handleLogin"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <!-- 错误信息显示 -->
          <div v-if="authStore.error" class="error-message">
            <n-icon color="#d03050">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </n-icon>
            <span>{{ authStore.error }}</span>
          </div>

          <!-- 登录按钮 -->
          <n-form-item>
            <n-button
              type="primary"
              size="large"
              block
              :loading="authStore.loading"
              @click="handleLogin"
            >
              {{ authStore.loading ? '登录中...' : '登录' }}
            </n-button>
          </n-form-item>
        </n-form>

        <!-- 提示信息 -->
        <div class="login-hint">
          <p>提示：确实</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

// 表单引用
const formRef = ref(null)

// 表单数据
const formData = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const formRules = {
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur']
    }
  ]
}

// 处理登录
async function handleLogin() {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    // 执行登录
    await authStore.login({
      username: formData.username,
      password: formData.password
    })
    
    message.success('登录成功')
    
    // 跳转到首页
    router.push({ name: 'Home' })
  } catch (error) {
    // 验证失败或登录失败，错误信息已经在store中处理
    if (error.message && !authStore.error) {
      message.error(error.message)
    }
  }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push({ name: 'Home' })
  }
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: var(--bg-primary);
  border-radius: 12px;
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.login-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-error);
  font-size: 14px;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(208, 48, 80, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(208, 48, 80, 0.2);
}

.login-hint {
  text-align: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.login-hint p {
  font-size: 14px;
  color: var(--text-tertiary);
  margin: 0;
}

.login-hint code {
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: var(--color-primary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-page {
    padding: var(--spacing-md);
  }
  
  .login-card {
    padding: var(--spacing-xl);
  }
  
  .login-title {
    font-size: 24px;
  }
}
</style>
