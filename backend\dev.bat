@echo off
echo XR Backend 开发环境启动
echo.

REM 检查配置文件
if not exist "finalUrl.txt" (
    echo 创建 finalUrl.txt
    echo www.xiu01.top > finalUrl.txt
)

REM 设置环境变量
set GIN_MODE=release

echo 功能说明:
echo   * 使用 go run 直接运行代码
echo   * 修改代码后手动重启 (Ctrl+C 然后重新运行)
echo   * 适合简单开发和调试
echo.

echo API地址:
echo   http://127.0.0.1:3332/ping
echo   http://127.0.0.1:3332/api/gallery/list
echo   http://127.0.0.1:3332/scheduler/status
echo.

echo 启动服务...
echo 按 Ctrl+C 停止服务
echo.

REM 启动服务
go run cmd/main.go

echo.
echo 服务已停止
pause
