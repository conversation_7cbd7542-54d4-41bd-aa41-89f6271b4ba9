package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"time"
	"xr-gallery/internal/repository"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// ImageUploadService 图片上传服务
type ImageUploadService struct {
	client    *http.Client
	uploadURL string
}

// NewImageUploadService 创建图片上传服务实例
func NewImageUploadService() *ImageUploadService {
	return &ImageUploadService{
		client: &http.Client{
			Timeout: 60 * time.Second,
		},
		uploadURL: "https://img1.101616.xyz/upload",
	}
}

// UploadResult 上传结果
type UploadResult struct {
	Src string `json:"src"`
}

// Reurl 内页图片上传处理器
func Reurl(ctx context.Context, c *app.RequestContext) {
	log.Printf("🖼️ 开始处理图片上传请求")

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 获取待处理图片记录
	record, err := repo.GetPendingImageRecord()
	if err != nil {
		log.Printf("❌ 获取待处理图片记录失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("获取待处理图片记录失败: %v", err),
		})
		return
	}

	if record == nil {
		log.Printf("📭 没有待处理的图片记录")
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success": false,
			"message": "no_records",
		})
		return
	}

	if record.OURL == "" {
		log.Printf("❌ 图片URL为空: id=%d", record.ID)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"success": false,
			"error":   "图片URL为空",
			"id":      record.ID,
		})
		return
	}

	log.Printf("🎯 处理图片: id=%d, xrid=%d, ourl=%s", record.ID, record.XRID, record.OURL)

	// 检查是否已经处理过（防重复上传）
	existingReurl, err := repo.CheckExistingReurl(record.OURL)
	if err != nil {
		log.Printf("⚠️  检查重复失败: %v", err)
		// 继续处理，不阻止上传
	} else if existingReurl != "" {
		log.Printf("♻️  发现重复图片，复用已上传结果: %s -> %s", record.OURL, existingReurl)

		// 更新当前记录
		if err := repo.UpdateImageReurl(record.ID, existingReurl); err != nil {
			log.Printf("⚠️  更新重复结果失败: %v", err)
		}

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success": true,
			"reason":  "reused_existing",
			"id":      record.ID,
			"ourl":    record.OURL,
			"reurl":   existingReurl,
		})
		return
	}

	// 标记为处理中
	if err := repo.UpdateImageReurl(record.ID, "processing"); err != nil {
		log.Printf("⚠️  更新处理状态失败: %v", err)
	}

	// 创建上传服务并处理图片
	uploadService := NewImageUploadService()

	// 构建完整图片URL
	fullImageURL, err := buildImageURL(record.OURL)
	if err != nil {
		log.Printf("❌ 构建图片URL失败: %v", err)
		repo.UpdateImageReurl(record.ID, "4040")
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"id":      record.ID,
			"error":   fmt.Sprintf("构建图片URL失败: %v", err),
		})
		return
	}

	log.Printf("🌐 完整图片URL: %s", fullImageURL)

	// 上传图片
	result, err := uploadService.UploadImage(fullImageURL)
	if err != nil {
		log.Printf("❌ 上传图片失败: id=%d, error=%v", record.ID, err)

		// 上传失败，标记为4040
		repo.UpdateImageReurl(record.ID, "4040")

		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"id":      record.ID,
			"xrid":    record.XRID,
			"error":   fmt.Sprintf("上传图片失败: %v", err),
		})
		return
	}

	// 更新数据库
	if err := repo.UpdateImageReurl(record.ID, result.Src); err != nil {
		log.Printf("❌ 更新reurl失败: %v", err)
	}

	log.Printf("🎉 图片上传成功: id=%d, %s -> %s", record.ID, record.OURL, result.Src)

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"success": true,
		"id":      record.ID,
		"xrid":    record.XRID,
		"ourl":    record.OURL,
		"reurl":   result.Src,
	})
}

// UploadImage 上传图片到Telegraph
func (s *ImageUploadService) UploadImage(imageURL string) (*UploadResult, error) {
	log.Printf("🚀 开始上传图片: %s", imageURL)

	// 下载图片数据
	imageData, err := s.downloadImage(imageURL)
	if err != nil {
		return nil, fmt.Errorf("下载图片失败: %w", err)
	}

	// 上传到Telegraph
	result, err := s.uploadToTelegraph(imageData)
	if err != nil {
		return nil, fmt.Errorf("上传到Telegraph失败: %w", err)
	}

	log.Printf("🎉 上传成功: %s -> %s", imageURL, result.Src)
	return result, nil
}

// downloadImage 下载图片数据
func (s *ImageUploadService) downloadImage(imageURL string) ([]byte, error) {
	resp, err := s.client.Get(imageURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("下载图片失败: HTTP %d", resp.StatusCode)
	}

	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if len(imageData) == 0 {
		return nil, fmt.Errorf("下载的图片数据为空")
	}

	log.Printf("📥 图片下载成功: %d bytes", len(imageData))
	return imageData, nil
}

// uploadToTelegraph 上传到Telegraph服务器
func (s *ImageUploadService) uploadToTelegraph(imageData []byte) (*UploadResult, error) {
	// 创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件字段
	part, err := writer.CreateFormFile("file", "image.jpg")
	if err != nil {
		return nil, err
	}

	if _, err := part.Write(imageData); err != nil {
		return nil, err
	}

	if err := writer.Close(); err != nil {
		return nil, err
	}

	// 发送上传请求
	req, err := http.NewRequest("POST", s.uploadURL, &buf)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("❌ 上传HTTP状态码错误: %d", resp.StatusCode)
		return &UploadResult{Src: "4040"}, nil
	}

	// 解析响应
	responseText, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 简单解析Telegraph响应格式
	if src := s.extractSrcFromResponse(string(responseText)); src != "" {
		return &UploadResult{Src: src}, nil
	}

	log.Printf("❌ 无法解析上传响应: %s", string(responseText))
	return &UploadResult{Src: "4040"}, nil
}

// extractSrcFromResponse 从Telegraph响应中提取src字段
func (s *ImageUploadService) extractSrcFromResponse(responseText string) string {
	// 简单的字符串解析，寻找 "src":"/file/..." 模式
	if strings.Contains(responseText, `"src":"`) {
		start := strings.Index(responseText, `"src":"`)
		if start != -1 {
			start += 7 // 跳过 "src":"
			end := strings.Index(responseText[start:], `"`)
			if end != -1 {
				src := responseText[start : start+end]
				if strings.HasPrefix(src, "/file/") {
					return src
				}
			}
		}
	}
	return ""
}

// buildImageURL 构建完整图片URL
func buildImageURL(relativeURL string) (string, error) {
	// 如果已经是完整URL，直接返回
	if strings.HasPrefix(relativeURL, "http://") || strings.HasPrefix(relativeURL, "https://") {
		return relativeURL, nil
	}

	// 读取基础URL配置
	baseURL, err := getBaseURL()
	if err != nil {
		return "", err
	}

	proxyPrefix := "https://re.101616.xyz/"

	// 构建完整URL
	var fullURL string
	if strings.HasPrefix(relativeURL, "/") {
		fullURL = fmt.Sprintf("%s%s%s", proxyPrefix, baseURL, relativeURL)
	} else {
		fullURL = fmt.Sprintf("%s%s/%s", proxyPrefix, baseURL, relativeURL)
	}

	return fullURL, nil
}

// getBaseURL 获取基础URL
func getBaseURL() (string, error) {
	content, err := os.ReadFile("finalUrl.txt")
	if err != nil {
		// 如果文件不存在，创建默认文件
		defaultURL := "www.xiu01.top"
		if err := os.WriteFile("finalUrl.txt", []byte(defaultURL), 0644); err != nil {
			return "", fmt.Errorf("创建默认URL文件失败: %w", err)
		}
		return defaultURL, nil
	}

	baseURL := strings.TrimSpace(string(content))
	if baseURL == "" {
		return "", fmt.Errorf("URL文件为空")
	}

	// 清理URL格式
	baseURL = strings.TrimPrefix(baseURL, "http://")
	baseURL = strings.TrimPrefix(baseURL, "https://")
	baseURL = strings.TrimSuffix(baseURL, "/")

	return baseURL, nil
}
