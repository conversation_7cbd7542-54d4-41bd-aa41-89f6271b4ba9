# XR Gallery Project - .gitignore 说明

## 📁 项目结构

本项目包含三个子项目：
- **frontend/** - Vue.js 前端项目
- **backend/** - Go 后端项目  
- **xr-rust/** - Rust 爬虫项目

## 🚫 忽略的文件和目录

### Frontend (Vue.js/Node.js)
```
frontend/node_modules/          # 依赖包
frontend/dist/                  # 构建输出
frontend/test-results/          # 测试结果
frontend/playwright-report/     # Playwright测试报告
frontend/coverage/              # 代码覆盖率报告
```

### Backend (Go)
```
backend/dist/                   # 构建输出
backend/tmp/                    # Air热加载临时文件
backend/*.exe                   # Windows可执行文件
backend/main                    # Linux可执行文件
backend/build-errors.log        # Air构建错误日志
backend/nodejs-test/            # Node.js测试目录
```

### xr-rust (Rust)
```
xr-rust/target/                 # Rust构建输出
xr-rust/xr-crawler-*           # 编译的可执行文件
xr-rust/.cross/                 # 交叉编译缓存
```

### 通用忽略
```
.env*                          # 环境变量文件
*.log                          # 日志文件
tmp/                           # 临时目录
*.backup                       # 备份文件
.vscode/                       # VS Code配置
.idea/                         # JetBrains IDE配置
```

## ✅ 包含的重要文件

以下文件**不会**被忽略，会被提交到git：

### 配置文件
- `frontend/package.json` - 前端依赖配置
- `frontend/vite.config.js` - Vite构建配置
- `backend/go.mod` - Go模块配置
- `backend/config/*.yaml` - 后端配置文件
- `xr-rust/Cargo.toml` - Rust项目配置
- `finalUrl.txt` - 爬虫URL配置

### 源代码
- `frontend/src/` - 前端源代码
- `backend/internal/` - 后端源代码
- `backend/cmd/` - 后端入口
- `xr-rust/src/` - Rust源代码

### 部署文件
- `backend/deploy/` - 部署脚本和配置
- `*.sh` - Shell脚本
- `*.ps1` - PowerShell脚本
- `Dockerfile*` - Docker配置

## 🔧 使用方法

### 初始化Git仓库
```bash
git init
git add .gitignore
git commit -m "Add .gitignore for multi-language project"
```

### 检查忽略状态
```bash
# 检查特定文件是否被忽略
git check-ignore frontend/node_modules/
git check-ignore backend/tmp/
git check-ignore xr-rust/target/

# 查看所有被忽略的文件
git status --ignored
```

### 添加文件到仓库
```bash
# 添加所有未被忽略的文件
git add .

# 查看将要提交的文件
git status
```

## 📝 自定义忽略规则

如果需要添加项目特定的忽略规则，可以编辑 `.gitignore` 文件：

```bash
# 添加自定义忽略规则
echo "my-custom-file.txt" >> .gitignore
echo "custom-directory/" >> .gitignore
```

## ⚠️ 注意事项

1. **已跟踪的文件**: 如果文件已经被git跟踪，添加到.gitignore后不会自动停止跟踪
   ```bash
   # 停止跟踪已跟踪的文件
   git rm --cached filename
   ```

2. **全局忽略**: 某些IDE和系统文件可以通过全局.gitignore忽略
   ```bash
   # 设置全局gitignore
   git config --global core.excludesfile ~/.gitignore_global
   ```

3. **大小写敏感**: 在Windows上注意文件名大小写问题

## 🚀 最佳实践

1. **提交.gitignore**: 确保.gitignore文件被提交到仓库
2. **早期设置**: 在项目开始时就设置好.gitignore
3. **定期更新**: 随着项目发展更新忽略规则
4. **团队协作**: 确保团队成员都使用相同的.gitignore规则

## 📚 相关文档

- [Git .gitignore 官方文档](https://git-scm.com/docs/gitignore)
- [GitHub .gitignore 模板](https://github.com/github/gitignore)
- [gitignore.io - 在线生成器](https://www.toptal.com/developers/gitignore)
