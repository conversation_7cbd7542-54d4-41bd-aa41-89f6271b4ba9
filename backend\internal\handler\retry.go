package handler

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"
	"xr-gallery/internal/repository"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// Retry 图片上传重试处理器
func Retry(ctx context.Context, c *app.RequestContext) {
	log.Printf("🔄 开始处理图片上传重试请求")

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 解析参数
	codesParam := c.DefaultQuery("codes", "4500,4503,4040")
	limitParam := c.DefaultQuery("limit", "10")

	// 解析错误码列表
	codes := strings.Split(codesParam, ",")
	for i, code := range codes {
		codes[i] = strings.TrimSpace(code)
	}

	// 解析限制数量
	limit, err := strconv.Atoi(limitParam)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50 // 最大50条
	}

	log.Printf("🔍 重试参数: codes=%v, limit=%d", codes, limit)

	// 获取失败的记录
	failedRecords, err := repo.GetFailedImageRecords(codes, limit)
	if err != nil {
		log.Printf("❌ 获取失败记录失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"success": false,
			"error":   fmt.Sprintf("获取失败记录失败: %v", err),
		})
		return
	}

	if len(failedRecords) == 0 {
		log.Printf("📭 没有找到需要重试的失败记录")
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"success":        true,
			"message":        "没有找到需要重试的失败记录",
			"total":          0,
			"processedCount": 0,
		})
		return
	}

	log.Printf("📋 找到 %d 条失败记录，开始重新处理", len(failedRecords))

	// 创建上传服务
	uploadService := NewImageUploadService()
	var results []map[string]interface{}
	var successCount int

	// 逐个重试处理
	for _, record := range failedRecords {
		log.Printf("🔄 重新处理: id=%d, xrid=%d", record.ID, record.XRID)

		// 重置为processing状态
		if err := repo.UpdateImageReurl(record.ID, "processing"); err != nil {
			log.Printf("⚠️  更新处理状态失败: %v", err)
		}

		if record.OURL == "" {
			results = append(results, map[string]interface{}{
				"success": false,
				"id":      record.ID,
				"error":   "图片URL为空",
			})
			continue
		}

		// 构建完整图片URL
		fullURL, err := buildImageURL(record.OURL)
		if err != nil {
			log.Printf("❌ 构建URL失败: id=%d, error=%v", record.ID, err)
			repo.UpdateImageReurl(record.ID, "4040")

			results = append(results, map[string]interface{}{
				"success": false,
				"id":      record.ID,
				"error":   fmt.Sprintf("构建URL失败: %v", err),
			})
			continue
		}

		// 上传图片
		uploadResult, err := uploadService.UploadImage(fullURL)
		if err != nil {
			log.Printf("❌ 重试上传失败: id=%d, error=%v", record.ID, err)
			repo.UpdateImageReurl(record.ID, "4040")

			results = append(results, map[string]interface{}{
				"success": false,
				"id":      record.ID,
				"error":   fmt.Sprintf("上传失败: %v", err),
			})
		} else {
			// 更新数据库
			if err := repo.UpdateImageReurl(record.ID, uploadResult.Src); err != nil {
				log.Printf("❌ 更新reurl失败: %v", err)
			}

			isSuccess := strings.HasPrefix(uploadResult.Src, "/file/")
			if isSuccess {
				successCount++
			}

			results = append(results, map[string]interface{}{
				"success": isSuccess,
				"id":      record.ID,
				"xrid":    record.XRID,
				"ourl":    record.OURL,
				"reurl":   uploadResult.Src,
			})

			log.Printf("✅ 重试成功: id=%d, %s -> %s", record.ID, record.OURL, uploadResult.Src)
		}

		// 延迟避免过快请求
		time.Sleep(2 * time.Second)
	}

	total := len(results)
	failure := total - successCount
	var successRate float64
	if total > 0 {
		successRate = float64(successCount) / float64(total) * 100.0
	}

	log.Printf("📊 重试完成: %d条记录, 成功%d条, 失败%d条, 成功率%.1f%%",
		total, successCount, failure, successRate)

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"success":        true,
		"total":          total,
		"processedCount": successCount,
		"successCount":   successCount,
		"failureCount":   failure,
		"successRate":    fmt.Sprintf("%.1f%%", successRate),
		"results":        results,
		"performance": utils.H{
			"go_powered":     true,
			"memory_safe":    true,
			"fast_execution": true,
			"concurrent":     true,
		},
	})
}
