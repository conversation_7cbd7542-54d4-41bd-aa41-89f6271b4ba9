[Unit]
Description=XR Backend Service
Documentation=https://github.com/your-org/xr-gallery
After=network.target mysql.service
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/xr/backend
ExecStart=/root/xr/backend/main_linux_amd64
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 环境变量
Environment=GIN_MODE=release
Environment=TZ=Asia/Shanghai

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=xr-backend

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/root/xr/backend
ReadWritePaths=/logs

[Install]
WantedBy=multi-user.target
