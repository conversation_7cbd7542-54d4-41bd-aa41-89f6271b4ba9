# XR Backend 开发环境

## 🔥 热加载开发

本项目支持热加载开发，修改代码后自动重新编译和重启，无需手动杀掉进程。

### 快速开始

#### Windows
```powershell
# 启动开发环境（推荐）
.\dev.ps1

# 启动调试模式
.\dev.ps1 debug
```

#### Linux/Mac
```bash
# 给脚本添加执行权限（首次运行）
chmod +x dev.sh

# 启动开发环境
./dev.sh

# 启动调试模式
./dev.sh debug
```

### 功能特性

- 🔥 **代码热加载** - 修改 `.go` 文件自动重新编译
- 📊 **实时日志** - 彩色输出，易于调试
- 🛡️ **错误处理** - 编译错误不会中断服务
- ⚡ **快速重启** - 1秒内完成重新加载
- 🎯 **智能监控** - 只监控相关文件，忽略临时文件

### 监控的文件类型

- `.go` - Go源代码文件
- `.yaml`, `.yml` - 配置文件
- `.json` - JSON配置文件
- `.html`, `.tpl`, `.tmpl` - 模板文件

### 忽略的目录

- `tmp/` - 临时编译文件
- `dist/` - 构建输出目录
- `vendor/` - 依赖包目录
- `testdata/` - 测试数据
- `node_modules/` - Node.js依赖
- `nodejs-test/` - Node.js测试目录

### 开发工作流

1. **启动开发环境**
   ```powershell
   .\dev.ps1
   ```

2. **修改代码**
   - 编辑任何 `.go` 文件
   - 保存文件后自动触发重新编译

3. **查看日志**
   - 编译状态实时显示
   - 服务启动状态彩色输出
   - 错误信息详细显示

4. **测试API**
   ```bash
   # 健康检查
   curl http://127.0.0.1:3332/ping
   
   # 调度器状态
   curl http://127.0.0.1:3332/scheduler/status
   
   # 图库列表
   curl http://127.0.0.1:3332/api/gallery/list
   ```

### 快捷键

- `Ctrl+C` - 停止开发服务
- `r + Enter` - 手动重启服务

### 配置文件

#### .air.toml
Air工具的配置文件，控制热加载行为：

```toml
[build]
  cmd = "go build -o ./tmp/main ./cmd/main.go"  # 编译命令
  bin = "./tmp/main"                            # 可执行文件路径
  delay = 1000                                  # 重启延迟(ms)
  
[build.include_ext]
  ["go", "yaml", "yml", "json", "html"]        # 监控的文件扩展名
  
[build.exclude_dir]
  ["tmp", "dist", "vendor", "node_modules"]    # 忽略的目录
```

### 环境变量

- `GIN_MODE=release` - 生产模式（默认）
- `GIN_MODE=debug` - 调试模式（使用 `debug` 参数启动）

### 故障排除

#### 1. Air未安装
```bash
# 手动安装
go install github.com/air-verse/air@latest
```

#### 2. 端口被占用
```bash
# Windows - 查找占用端口的进程
netstat -ano | findstr :3332

# Linux/Mac - 查找占用端口的进程
lsof -i :3332

# 杀掉进程
taskkill /PID <PID> /F  # Windows
kill -9 <PID>          # Linux/Mac
```

#### 3. 编译错误
- 检查Go语法错误
- 查看 `build-errors.log` 文件
- 确保所有依赖已安装：`go mod tidy`

#### 4. 配置文件缺失
```bash
# 检查必要文件
ls config/config.yaml  # 配置文件
ls finalUrl.txt        # URL配置
ls .air.toml           # Air配置
```

### 生产环境部署

开发完成后，使用以下命令构建生产版本：

```powershell
# Windows
.\build_and_run.ps1

# 或使用部署脚本
.\deploy\deploy.sh
```

### API端点

| 端点 | 描述 |
|------|------|
| `GET /ping` | 健康检查 |
| `GET /api/gallery/list` | 图库列表 |
| `GET /api/gallery/detail/:xrid` | 图库详情 |
| `GET /getlist` | 手动爬取 |
| `GET /scheduler/start` | 启动调度器 |
| `GET /scheduler/stop` | 停止调度器 |
| `GET /scheduler/status` | 调度器状态 |

### 开发建议

1. **代码修改频繁时**：使用热加载可以大大提高开发效率
2. **调试API时**：使用 `debug` 模式获得更详细的日志
3. **测试调度器时**：可以通过API手动控制调度器状态
4. **数据库操作时**：注意查看日志中的SQL执行情况

### 性能优化

- 热加载只在开发环境使用
- 生产环境使用编译后的二进制文件
- 合理设置监控文件类型，避免不必要的重编译
