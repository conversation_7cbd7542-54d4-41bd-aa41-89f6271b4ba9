package handler

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"
	"xr-gallery/internal/scheduler"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

var crawlerScheduler *scheduler.CrawlerScheduler

// InitScheduler 初始化调度器
func InitScheduler(host string, port int) {
	baseURL := fmt.Sprintf("http://%s:%d", host, port)
	crawlerScheduler = scheduler.NewCrawlerScheduler(baseURL)
	log.Printf("📅 爬虫调度器已初始化，baseURL: %s", baseURL)
}

// StartSchedulerOnBoot 服务启动时自动启动调度器
func StartSchedulerOnBoot() error {
	if crawlerScheduler == nil {
		return fmt.Errorf("调度器未初始化")
	}

	if crawlerScheduler.IsRunning() {
		return nil // 已经在运行
	}

	return crawlerScheduler.Start()
}

// StopSchedulerOnShutdown 服务关闭时停止调度器
func StopSchedulerOnShutdown() {
	if crawlerScheduler != nil && crawlerScheduler.IsRunning() {
		log.Printf("🛑 正在停止调度器...")
		crawlerScheduler.Stop()
		log.Printf("✅ 调度器已停止")
	}
}

// StartScheduler 启动调度器
func StartScheduler(ctx context.Context, c *app.RequestContext) {
	log.Printf("🚀 收到启动调度器请求")

	if crawlerScheduler == nil {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "调度器未初始化",
		})
		return
	}

	if crawlerScheduler.IsRunning() {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"code":    200,
			"message": "调度器已在运行",
			"data":    crawlerScheduler.GetStats(),
		})
		return
	}

	err := crawlerScheduler.Start()
	if err != nil {
		log.Printf("❌ 启动调度器失败: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "启动调度器失败",
			"error":   err.Error(),
		})
		return
	}

	log.Printf("✅ 调度器启动成功")
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "调度器启动成功",
		"data":    crawlerScheduler.GetStats(),
	})
}

// StopScheduler 停止调度器
func StopScheduler(ctx context.Context, c *app.RequestContext) {
	log.Printf("🛑 收到停止调度器请求")

	if crawlerScheduler == nil {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "调度器未初始化",
		})
		return
	}

	if !crawlerScheduler.IsRunning() {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"code":    200,
			"message": "调度器未在运行",
			"data":    crawlerScheduler.GetStats(),
		})
		return
	}

	crawlerScheduler.Stop()

	log.Printf("✅ 调度器停止成功")
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "调度器停止成功",
		"data":    crawlerScheduler.GetStats(),
	})
}

// GetSchedulerStatus 获取调度器状态
func GetSchedulerStatus(ctx context.Context, c *app.RequestContext) {
	if crawlerScheduler == nil {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "调度器未初始化",
		})
		return
	}

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "success",
		"data":    crawlerScheduler.GetStats(),
	})
}

// SetSchedulerInterval 设置调度器间隔
func SetSchedulerInterval(ctx context.Context, c *app.RequestContext) {
	intervalStr := c.DefaultQuery("interval", "60")

	intervalSeconds, err := strconv.Atoi(intervalStr)
	if err != nil || intervalSeconds < 10 {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"code":    400,
			"message": "间隔时间无效，最少10秒",
		})
		return
	}

	if crawlerScheduler == nil {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "调度器未初始化",
		})
		return
	}

	interval := time.Duration(intervalSeconds) * time.Second
	crawlerScheduler.SetInterval(interval)

	log.Printf("🔧 调度器间隔已更新: %v", interval)
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "间隔时间更新成功",
		"data":    crawlerScheduler.GetStats(),
	})
}

// SetSchedulerMaxPages 设置调度器最大页数
func SetSchedulerMaxPages(ctx context.Context, c *app.RequestContext) {
	maxPagesStr := c.DefaultQuery("max_pages", "10")

	maxPages, err := strconv.Atoi(maxPagesStr)
	if err != nil || maxPages < 1 || maxPages > 100 {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"code":    400,
			"message": "最大页数无效，范围1-100",
		})
		return
	}

	if crawlerScheduler == nil {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "调度器未初始化",
		})
		return
	}

	crawlerScheduler.SetMaxPages(maxPages)

	log.Printf("🔧 调度器最大页数已更新: %d", maxPages)
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "最大页数更新成功",
		"data":    crawlerScheduler.GetStats(),
	})
}
